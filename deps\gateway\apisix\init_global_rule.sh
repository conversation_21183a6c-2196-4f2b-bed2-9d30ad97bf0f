#!/bin/bash

set -e

curl --request PUT \
  --url http://127.0.0.1:9180/apisix/admin/global_rules/1 \
  --header 'X-API-KEY: 8aOWcEqGY4gHwPh2deX6zFSvoLuQKBJk' \
  --header 'content-type: application/json' \
  --data '{
    "id": "1",
    "plugins": {
        "asec-watermark": {
            "_meta": {
                "disable": false
            },
            "enable": false
        },
        "asec-kafka-logger": {
            "_meta": {
                "disable": false
            },
            "batch_max_size": 1000,
            "brokers": [
                {
                    "host": "asec_platform_host",
                    "port": 39092
                }
            ],
            "platform_addr":"asec_platform_host",
            "buffer_duration": 60,
            "cluster_name": 1,
            "inactive_timeout": 5,
            "include_req_body": true,
            "include_resp_body": true,
            "kafka_topic": "seven_access_log",
            "log_format": {
                "request_id": "$http_X-Request-Id",
                "access_time": "$Asec_Access_Time",
                "user_id": "$Asec-User-Id",
                "user_name": "$Asec-User-Name",
                "app_id": "$Asec_App_Id",
                "app_name": "$Asec_App_Name",
                "url": "$http_Asec_Full_Url",
                "strategy_id": "$Asec_Strategy_Id",
                "strategy_name": "$Asec_Strategy_Name",
                "strategy_action": "$Asec_Strategy_Action",
                "app_type": "$Asec_App_Type",
                "client_ip": "$remote_addr",
                "host": "$host",
                "uri": "$uri",
                "pid": "$pid",
                "etag": "$sent_http_ETag",
                "server_addr": "$Asec_Server_Addr",
                "schema": "$scheme",
                "req_accept": "$http_Accept",
                "req_accept_encoding": "$http_Accept-Encoding",
                "req_accept_language": "$http_Accept-Language",
                "req_body": "$request_body",
                "req_content_encoding": "$http_Content-Encoding",
                "req_content_length": "$http_Content-Length",
                "req_content_type": "$http_Content-Type",
                "req_cookie": "$http_Cookie",
                "req_method": "$request_method",
                "req_origin": "$http_Origin",
                "req_proto": "$request_proto",
                "req_query_args": "$query_string",
                "req_referer": "$http_Referer",
                "req_transfer_encoding": "$http_Transfer-Encoding",
                "req_user_agent": "$http_User-Agent",
                "req_x_forwarded_for": "$http_X-Forwarded-For",
                "req_x_request_with": "$http_X-Requested-With",
                "rsp_body": "$resp_body",
                "rsp_date": "$sent_http_Date",
                "rsp_location": "$sent_http_Location",
                "rsp_server": "$server_name",
                "rsp_status_code": "$status",
                "rsp_upstream_code": "$upstream_status",
                "rsp_cache_control": "$rsp_cache_control",
                "rsp_content_disposition": "$rsp_content_disposition",
                "rsp_content_encoding": "$rsp_content_encoding",
                "rsp_content_length": "$rsp_content_length",
                "rsp_content_type": "$rsp_content_type",
                "rsp_cookie": "$rsp_cookie",
                "rsp_transfer_encoding": "$rsp_transfer_encoding",
                "rsp_set_cookie": "$rsp_set_cookie",
                "rsp_last_modified": "$rsp_last_modified",
                "activity":"$asec_activity",
                "file_category_id":"$asec_category_id",
                "sensitive_info":"$asec_sensitive_info",
                "file_name":"$asec_file_name"
            },
            "max_retry_count": 0,
            "meta_format": "default",
            "meta_refresh_interval": 30,
            "name": "kafka logger",
            "producer_batch_num": 200,
            "producer_batch_size": 5242880,
            "producer_max_buffering": 50000,
            "producer_time_linger": 1,
            "producer_type": "async",
            "required_acks": 1,
            "retry_delay": 1,
            "static_resource_conf": {
                "enable": false,
                "file_types": [
                    "js",
                    "css",
                    "png",
                    "jpg",
                    "gif",
                    "bmp",
                    "svg",
                    "ico",
                    "woff",
                    "ttf",
                    "otf",
                    "eot",
                    "mp4",
                    "avi",
                    "flv",
                    "mpeg",
                    "mpg",
                    "mp3",
                    "wav",
                    "ogg",
                    "mov"
                ]
            },
            "timeout": 3
        },
        "request-id": {
            "_meta": {
                "disable": false
            },
            "include_in_response": true
        },
        "serverless-pre-function": {
            "_meta": {
                "disable": false
            },
            "phase": "access",
            "functions": [
                "return function(conf, ctx) local core = require(\"apisix.core\") local matched_route = ctx.matched_route if not matched_route then ngx.exec(\"/apisix-internal-404\") end end"
            ]
        }
    }
}'
