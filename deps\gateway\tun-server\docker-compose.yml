version: '3'
#需要先执行docker login --username=asdsec registry.cn-guangzhou.aliyuncs.com
### 密码：见飞书公告
services:
  tun-server:
    container_name: asec_gateway
    image: ${REGISTRY_URL:-registry.cn-guangzhou.aliyuncs.com}/asdsec/tun-server:${TUNSRV_TAG:-0.1}
    privileged: true
    network_mode: host
    command: "/usr/local/asec/tunsrv/tunsrv -config /usr/local/asec/tunsrv/config/config.json -sc /usr/local/asec/tunsrv/config/sidecar.yaml -mode 3 -code $GATEWAY_CODE"
    # ports:
    #   - "8443:8443"
    #   - "8443:8443/udp"
    # networks:
    #   - platform-network
    restart: always
    volumes:
      - ./config:/data/conf
      - /dev/mem:/dev/mem   #dmidecode使用
      - ./apisix/download:/usr/local/download
      - ./runningcfg:/usr/local/asec/tunsrv/config
      - ./sidecarlog:/usr/local/asec/tunsrv/log
      - ./gatewaylog:/usr/local/asec/tunsrv/logs
    environment:
      ASEC_PLATFORM_HOST: $ASEC_PLATFORM_DOMAIN
      ASEC_VIRTUAL_IP_HOST: $ASEC_GATEWAY_HOST
      ASEC_VIRTUAL_IP_PORT: $ASEC_VIRTUAL_IP_PORT
      TZ: Asia/Shanghai
      HOST_IP:
      GATEWAY_CODE: $GATEWAY_CODE
    extra_hosts:
      - "asec_platform_host:$ASEC_PLATFORM_HOST"
      - "apisix:127.0.0.1"
    cap_add:
      - NET_ADMIN
      - SYS_ADMIN
      - NET_RAW     
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "10"


# networks:
#   platform-network:
#     external:
#       name: platform_network