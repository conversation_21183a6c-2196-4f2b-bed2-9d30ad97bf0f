   [Unit]
   Description=Asec platform spa-node-ebpf
   After=network.target

   [Service]
   Type=simple
   EnvironmentFile=/opt/platform/current/spa/spa.conf
   ExecStart=/bin/sh -c '/opt/platform/current/spa/spa-node-ebpf -iface $SPA_EBPF_IFACE -spa-manager-url $SPA_MANAGER_URL -container-names $CONTAINER_NAMES -sync-master-url $SPA_MANAGER_URL'
   WorkingDirectory=/opt/platform/current/spa/
   Restart=always
   RestartSec=5s
   StandardOutput=syslog
   StandardError=syslog
   SyslogIdentifier=spa-node-ebpf

   [Install]
   WantedBy=multi-user.target