/*
* ================================================
* 版权归 湖南安数达科技有限公司 所有
* 作者：        Xy
* 描述：        跨平台敏感数据引擎导出接口(C导出)
* 生成时间：    2023/12/25
* =================================================
*/

#ifndef __ASEC_SENSITIVEENGINE_HPP__
#define __ASEC_SENSITIVEENGINE_HPP__

// 导出宏定义
#ifdef _WIN32
#ifdef ASECSENSITIVEENGINE_EXPORTS
#define ASEC_SENSITIVEENGINE_API __declspec(dllexport)
#else
#define ASEC_SENSITIVEENGINE_API __declspec(dllimport)
#endif
#endif


// 导出宏定义
#ifdef __linux__
#define ASEC_SENSITIVEENGINE_API __attribute__((visibility("default")))
#endif


// C导出
#ifdef __cplusplus
extern "C" {
#endif
    // 初始化并且设置配置数据库路径
    bool InitSensitiveEngine(const char* config_db_path, const char* run_dir);

    // 获取文件敏感性
    bool GetFileSensitive(const char* byte_param, char** byte_package);

    // 释放package数组
    void FreeSensitivePackage(char** byte_package);
#ifdef __cplusplus
}
#endif

#endif