version: '3'
#需要先执行docker login --username=asdsec registry.cn-guangzhou.aliyuncs.com
### 密码：见飞书公告
services:
  apisix-dashboard:
    container_name: apisix_dashboard
    image: registry.cn-guangzhou.aliyuncs.com/asdsec/apisix_dashboard:${APISIX_DASHBOARD_TAG:-3.0.1}
#    restart: no
    networks:
      - platform-network
    volumes:
      #- ./schema.json:/usr/local/apisix-dashboard/conf/schema.json
      - ./dashboard_conf/conf.yaml:/usr/local/apisix-dashboard/conf/conf.yaml
    ports:
      - "19090:9090"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "10"

  apisix:
    container_name: apisix
    image: registry.cn-guangzhou.aliyuncs.com/asdsec/apisix:${APISIX_IMAGE_TAG:-3.6.0}
    restart: always
    volumes:
      - ./apisix_conf/config.yaml:/usr/local/apisix/conf/config.yaml:ro
      - ./lua_plugin/asec-response-rewrite.lua:/usr/local/apisix/apisix/plugins/asec-response-rewrite.lua
      - ./lua_plugin/asec-forward-auth.lua:/usr/local/apisix/apisix/plugins/asec-forward-auth.lua
      - ./lua_plugin/asec-kafka-logger.lua:/usr/local/apisix/apisix/plugins/asec-kafka-logger.lua
      - ./lua_plugin/asec-response-process.lua:/usr/local/apisix/apisix/plugins/asec-response-process.lua
      - ./lua_plugin/asec-watermark.lua:/usr/local/apisix/apisix/plugins/asec-watermark.lua
      - ./lua_plugin/asec-identify-sensitive.lua:/usr/local/apisix/apisix/plugins/asec-identify-sensitive.lua
      - ./lua_plugin/asec-virtual-ip.lua:/usr/local/apisix/apisix/plugins/asec-virtual-ip.lua
      - ./lua_plugin/asec-url-control.lua:/usr/local/apisix/apisix/plugins/asec-url-control.lua
      - ./lua_plugin/asec-form-fill.lua:/usr/local/apisix/apisix/plugins/asec-form-fill.lua
      - ./lua_plugin/asec-source-ip-insert.lua:/usr/local/apisix/apisix/plugins/asec-source-ip-insert.lua
      - ./static:/usr/local/apisix/static
      - ./strategy_plugin:/usr/local/apisix/strategy_plugin
      - ./so_sdk:/usr/local/apisix/apisix/plugins/so_sdk
      - ./proto:/usr/local/apisix/apisix/include/apisix/model/proto
      - ./download:/usr/local/apisix/download
    environment:
      LD_LIBRARY_PATH: /usr/local/apisix/apisix/plugins/so_sdk
      LANG: C.UTF-8
      TZ: Asia/Shanghai
    depends_on:
      - etcd
    extra_hosts:
      - "asec_platform_host:$ASEC_PLATFORM_HOST"
      - "asec_virtual_ip_host:$ASEC_GATEWAY_HOST"
      - "platform_address:$ASEC_PLATFORM_HOST"
    networks:
      - platform-network
    ports:
      - "9180:9180/tcp"
      - "9094:9094/tcp"
      - "80:80/tcp"
      - "7443:7443/tcp"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "10"

  etcd:
    container_name: etcd
    image: registry.cn-guangzhou.aliyuncs.com/asdsec/etcd:${ETCD_TAG:-3.4.15}
    restart: always
    networks:
      - platform-network
    volumes:
      - etcd_data:/bitnami/etcd
      - ./etcd_conf/etcd.conf.yml:/opt/bitnami/etcd/conf/etcd.conf.yml
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://etcd:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
      TZ: Asia/Shanghai
    ports:
      - "2379:2379/tcp"
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "10"

networks:
  platform-network:
    external:
      name: platform_network

volumes:
  etcd_data:
    driver: local