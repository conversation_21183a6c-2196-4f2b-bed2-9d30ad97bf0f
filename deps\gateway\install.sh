#!/bin/bash

set -e


service_name=""

current_dir=$(cd "$(dirname "$0")"; pwd)
source $current_dir/version.conf
current_filepath="$current_dir/$(basename "$0")"

cmd_list=$@

#参数解析
ARGS=$(getopt -o m:s:p:k:h:e:f:c:g:d: --long mode,status,platform,es_key,help,env_file,code:,gateway:,domain: -- "$@")
eval set -- "$ARGS"

mode=""
status=""
platform_addr=""
es_key=""
env_file_path=""
eskey_file_path=""
gateway_code=0
gateway_addr=""
platform_domain=""

while true; do
    case $1 in
        -m|--mode)
            mode=$2
            shift 2
            ;;
        -s|--status)
            status=$2
            shift 2
            ;;
        -p|--platform)
            platform_addr=$2
            shift 2
            ;;
        -k|--es_key)
            es_key=$2
            shift 2
            ;;
        -e|--env_file)
            env_file_path=$2
            shift 2
            ;;
        -c|--code)
            gateway_code=$2
            shift 2
            ;;
        -g|--gateway)
            gateway_addr=$2
            shift 2
            ;;
        -d|--domain)
            platform_domain=$2
            shift 2
            ;;
        -f|--eskey_file)
            eskey_file_path=$2
            shift 2
            ;;
        -h|--help)
            cat usage
            exit 0
            ;;
        --)
            shift
            break
            ;;
        *)
            shift
            ;;
    esac
done

#前置判断
if [ -z $env_file_path ];then
    echo "please use the option -e/--env_file to provide the path of the environment file."
    exit -1
else
    source $env_file_path
fi

if [ -z "$mode" ];then
    echo "please provide the deployment mode using the -m/--mode option."
    exit -1
fi

if [ "$mode" != "se" ] && [ "$mode" != "gateway" ];then
    echo "the mode should be either \"se\" or \"gateway\"."
    exit -1
fi

if [ "$mode" == "gateway" ];then
    service_name=tunsrv
fi

if [ "$mode" == "se" ];then
    service_name=tunsrv-se
fi

if [ -z "$status" ];then
    echo "please provide the default startup state using the -s/--status option."
    exit -1
fi

if [ "$status" != "run" ] && [ "$status" != "stop" ];then
    echo "the auto state should be either \"run\" or \"stop\"."
    exit -1
fi

if [ -z "$platform_addr" ];then
    echo "please use the -p/--platform option to set the platform address."
    exit -1
fi

if [ -z "$gateway_addr" ];then
    echo "please use the -g/--gateway option to set the gateway address."
    exit -1
fi

set +e
if [ ! -x $current_dir/init.sh ];then
    chmod +x $current_dir/init.sh
fi
$current_dir/init.sh
if [ $? != 0 ]; then
    echo "restart"
    sleep 1
    $current_filepath $cmd_list
fi
set -e

chmod +x ${current_dir}/docker_installer.sh
${current_dir}/docker_installer.sh

if ! docker network inspect platform_network > /dev/null 2>&1; then
    docker network create platform_network
fi

function docker_login() {

    # 设置最大重试次数
    max_retries=5
    retry_count=0

    # 定义登录命令
    login_command="docker login --username=asdsec registry.cn-guangzhou.aliyuncs.com -p asd@1234!"

    # 循环重试直到成功或达到最大重试次数
    while true; do
      # 执行登录命令
      $login_command

      # 检查登录是否成功
      login_status=$?

      if [ $login_status -eq 0 ]; then
        echo "登录成功"
        break
      elif [ $retry_count -lt $max_retries ]; then
        # 增加重试计数
        retry_count=$((retry_count+1))
        echo "登录失败，重试 (${retry_count}/${max_retries})"
        sleep 1  # 可以增加一些延迟时间

        continue
      else
        echo "达到最大重试次数，登录失败"
        break
      fi
    done
}
# docker登录
docker_login

systemctl daemon-reload

IP=$(hostname -I | awk '{ print $1}')
export HOST_IP=$IP
export ASEC_PLATFORM_HOST=${platform_addr}
export ASEC_GATEWAY_HOST=${gateway_addr}
export GATEWAY_CODE=$gateway_code
export SPA_MANAGER_URL="https://${platform_addr}:4430"

# 如果没有指定 ASEC_PLATFORM_DOMAIN，则使用 ASEC_PLATFORM_HOST 的值
if [ -z "$platform_domain" ]; then
    export ASEC_PLATFORM_DOMAIN=${platform_addr}
else
    export ASEC_PLATFORM_DOMAIN=${platform_domain}
fi


function setup_firewall(){
    echo "Setting up firewall..."
    if [ -f /etc/os-release ]; then
        source /etc/os-release
        if [[ "$ID" == "ubuntu" ]]; then
            chmod +x "$current_dir"/ufw-config.sh
            "$current_dir"/ufw-config.sh
        elif [[ "$ID" == "centos" ]]; then
            echo "CentOS firewall configuration not implemented yet"
        fi
    fi
    echo "Firewall setup finished."
}

function install_tunsrv() {
    cd ${current_dir}/tun-server
    echo "SPA_MANAGER_URL=${SPA_MANAGER_URL}" >> .env
    # 检查 runningcfg 目录是否存在且非空
    if [ ! -d "./runningcfg" ] || [ -z "$(ls -A ./runningcfg 2>/dev/null)" ]; then
        echo "Initializing configuration and log directories..."
        mkdir -p ./runningcfg ./sidecarlog ./gatewaylog
        
        # 一次性从镜像中复制配置文件和日志目录结构
        docker run --rm \
            -v "$(pwd)/runningcfg:/tmp/config" \
            -v "$(pwd)/sidecarlog:/tmp/sidecarlog" \
            -v "$(pwd)/gatewaylog:/tmp/gatewaylog" \
            registry.cn-guangzhou.aliyuncs.com/asdsec/tun-server:${TUNSRV_TAG:-0.1} \
            sh -c "
                # 复制配置文件
                cp -r /usr/local/asec/tunsrv/config/* /tmp/config/
                
                # 复制日志目录结构（如果存在）
                if [ -d '/usr/local/asec/tunsrv/log' ]; then 
                    cp -r /usr/local/asec/tunsrv/log/* /tmp/sidecarlog/ 2>/dev/null || true
                fi
                if [ -d '/usr/local/asec/tunsrv/logs' ]; then 
                    cp -r /usr/local/asec/tunsrv/logs/* /tmp/gatewaylog/ 2>/dev/null || true
                fi
            "
        
        echo "You can now edit configuration files in ./runningcfg directory"
    else
        echo "Configuration directory already exists, skipping initialization"
        # 确保日志目录存在
        mkdir -p ./sidecarlog ./gatewaylog
    fi
    
    docker compose up -d
    echo "tun-server installed success!"
}
function install_webgw() {
    #Apisix安装部署
    apisix_dir=${current_dir}/apisix
    cd ${apisix_dir}
    ##替换参数
    #if [ ! -x ${apisix_dir}/strategy_plugin/conf/config.yaml ];then
    #    sed "s/platform_address/${platform_addr}/g" -i ${apisix_dir}/strategy_plugin/conf/config.yaml
    #fi

    docker compose up -d apisix etcd
    #等待容器起来
    sleep 10
    if [ ! -x ${apisix_dir}/init_global_rule.sh ];then
    #    sed "s/platform_address/${platform_addr}/g" -i ${apisix_dir}/init_global_rule.sh
       chmod +x ${apisix_dir}/init_global_rule.sh
       ${apisix_dir}/init_global_rule.sh
    fi
}

setup_firewall
# 安装 spa-node-ebpf 服务
function install_spa_ebpf_service() {
    echo "正在安装并启用 spa-node-ebpf 服务..."
    mkdir -p /opt/platform/current/spa
    # 获取默认网卡
    default_iface=$(ip -o link show | awk -F': ' '$2 != "lo" {print $2; exit}')

    # 复制并启用服务
    cp -f ${current_dir}/spa/spa-node-ebpf.service /etc/systemd/system/spa-node-ebpf.service

    # 先覆盖配置文件
    echo "SPA_EBPF_IFACE=$default_iface" > /opt/platform/current/spa/spa.conf
    echo "SPA_MANAGER_URL=${SPA_MANAGER_URL}" >> /opt/platform/current/spa/spa.conf
    echo "CONTAINER_NAMES=openresty,asec_gateway" >> /opt/platform/current/spa/spa.conf
    # 移动 spa/spa-node-ebpf 执行程序
    cp -f ${current_dir}/spa/spa-node-ebpf /opt/platform/current/spa/spa-node-ebpf
    systemctl daemon-reload
    systemctl enable spa-node-ebpf.service
    systemctl restart spa-node-ebpf.service
    systemctl --no-pager status spa-node-ebpf.service | grep "Active:" || true
    echo "spa-node-ebpf 服务已安装并启动。"
}
install_tunsrv
install_webgw

# 检查本地9000端口服务状态
if curl -s http://127.0.0.1:9000 > /dev/null 2>&1; then
    echo "spa-node-ebpf 服务已安装, 准备重启。"
    # 重启 spa-node-ebpf 服务
    echo "重启 spa-node-ebpf 服务..."
    systemctl restart spa-node-ebpf.service
else
    # curl 失败了，执行这里的逻辑
    echo "spa-node-ebpf 服务未安装或未运行，开始安装..."
    # 安装 spa-node-ebpf 服务
    install_spa_ebpf_service
fi