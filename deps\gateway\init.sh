#!/bin/bash

set -e




current_dir=$(cd "$(dirname "$0")"; pwd)

source $current_dir/version.conf

#安装所有依赖软件
file_list=$(find $current_dir/dependency -type f -name "*.sh" | sort)

#遍历脚本数组中的所有文件并执行
for file in ${file_list[@]}; do
    echo "run ${file}"

    if [ ! -x ${file} ]; then
        chmod u+x ${file}
    fi
    ${file}
done

# if [ ! -x $current_dir/get-pkg.sh ];then
#     chmod +x $current_dir/get-pkg.sh
# fi
# $current_dir/get-pkg.sh