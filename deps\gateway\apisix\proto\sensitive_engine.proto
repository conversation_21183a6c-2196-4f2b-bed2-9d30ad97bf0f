syntax = "proto3";

option java_package = "org.apache.apisix.api.sensitive_engine";
option java_outer_classname = "SensitiveEngineProto";
option java_multiple_files = true;
option go_package = "github.com/apache/apisix/api/sensitive_engine;sensitive_engine";

// 敏感引擎提取入参
message SensitiveExtractParam {
  // 匹配类型(掩码)
  enum MatchType {
    kInvalidType = 0;
    kTrace = 1;
    kFileType = 2;
    kFileAttr = 4;
    kFileName = 8;
    kFileContent = 16;
    kSourceProcAndUrl = 32;
  }

  // 匹配方式
  enum MatchMode {
    kInvalidMode = 0;
    // 文件路径
    kFilePath = 1;
    // 文件流
    kFileStream = 2;
  };

  string dlp_rule_id = 1;     // Dlp规则id(默认传空，拓展用)
  string file_path = 2;       // 文件路径
  bytes file_stream = 3;      // 文件流
  string src_proc = 4;        // 来源进程
  string src_url = 5;         // 来源url
  MatchType match_type = 6;   // 匹配类型(MatchType 必传)
  MatchMode match_mode=7;     // 匹配方式(MatchMode 必传)
  bool update_cache = 8;      // 是否更新缓存(客户端使用)
}

// 敏感元素信息
message SensitiveElementInfo {
  int32 rule_id = 1;                              // 敏感元素规则id
  string desc = 2;                                // 规则描述
  repeated string sensitive_element_data = 3;     // 命中的敏感元素内容
}

// 敏感数据信息
message SensitiveDataInfo {
  bool hit = 1;                                               // 是否命中
  string sensitive_data_id = 2;                               // 敏感数据ID
  string sensitive_data_name = 3;                             // 敏感数据名称
  int32 sensitive_data_level = 4;                             // 敏感数据等级
  string sensitive_data_category_id = 5;                      // 数据类别ID
  repeated string sensitive_source_id = 6;                    // 命中的来源规则ID
  repeated string src_paths = 7;                              // 命中的来源路径
  repeated SensitiveElementInfo file_name_result = 8;         // 文件名敏感信息
  repeated SensitiveElementInfo file_content_result = 9;      // 文件内容敏感信息
  repeated SensitiveElementInfo file_text_result = 10;        // 文本敏感信息 (只有在传文本的时候才有用)
  bool file_type_suffix = 11;                                 // 隐藏文件后缀(标识这个敏感数据是否开启文件隐藏后缀)
}

// 敏感引擎出参
message SensitiveExtractPackage {
  repeated string trace_id = 1;                               // trace_id
  repeated string sub_trace_id = 2;                           // sub_trace_id
  int32 category_id = 3;		                                // 文件类型id
  string extension_name = 4;                                  // 文件拓展名
  repeated SensitiveDataInfo sensitive_data_infos = 5;        // 命中的敏感数据
  bool file_type_suffix = 6;                                  // 隐藏文件后缀(与敏感数据SensitiveDataInfo里面的file_type_suffix不同，这个作为基础属性上报)
}