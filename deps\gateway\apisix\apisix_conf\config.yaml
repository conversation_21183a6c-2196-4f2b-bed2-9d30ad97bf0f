#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# If you want to set the specified configuration value, you can set the new
# in this file. For example if you want to specify the etcd address:
#
# deployment:
#   role: traditional
#   role_traditional:
#     config_provider: etcd
#   etcd:
#     host:
#       - http://127.0.0.1:2379
#
# To configure via environment variables, you can use `${{VAR}}` syntax. For instance:
#
# deployment:
#   role: traditional
#   role_traditional:
#     config_provider: etcd
#   etcd:
#     host:
#       - http://${{ETCD_HOST}}:2379
#
# And then run `export ETCD_HOST=$your_host` before `make init`.
#
# If the configured environment variable can't be found, an error will be thrown.
#
# Also, If you want to use default value when the environment variable not set,
# Use `${{VAR:=default_value}}` instead. For instance:
#
# deployment:
#   role: traditional
#   role_traditional:
#     config_provider: etcd
#   etcd:
#     host:
#       - http://${{ETCD_HOST:=localhost}}:2379
#
# This will find environment variable `ETCD_HOST` first, and if it's not exist it will use `localhost` as default value.
#
apisix:
  ssl:
    enable: true
    fallback_sni: "default.asdsec.com"
    listen:
      - port: 7443
        enable_http2: true
  node_listen: 80              # APISIX http listening port
  enable_ipv6: false
  enable_control: true
  control:
    ip: "127.0.0.1"
    port: 9094
  # enable_debug: true

nginx_config:
  error_log_level: "warn"
  envs:
    - LANG
    - LD_LIBRARY_PATH
  http_server_configuration_snippet: |
    location = /apisix-internal-404 {
      internal;
      access_by_lua_block {
        local accept = ngx.var.http_accept or ""
        ngx.status = 404
        if string.find(accept, "application/json") then
          ngx.header["Content-Type"] = "application/json; charset=utf-8"
          ngx.say('{"error_msg": "Not Found"}')
        else
          -- 返回自定义的 HTML 错误页面
          local file = io.open("/usr/local/apisix/static/error.html", "r")
          if file then
            local content = file:read("*all")
            file:close()
            ngx.header["Content-Type"] = "text/html; charset=utf-8"
            ngx.say(content)
          else
            -- 如果文件不存在，返回默认错误页面
            ngx.header["Content-Type"] = "text/html; charset=utf-8"
            ngx.say('<html><head><title>404 Not Found</title></head><body><center><h1>404 Not Found</h1></center></html>')
          end
        end
        ngx.exit(404)
      }
    }

    location ~ ^/\.asec-gateway/asec-internal/js/(.+\.js)$ {
        root /usr/local/apisix/static;
        try_files /$1 =404;
        expires 1d;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

deployment:
  admin:
    allow_admin: # https://nginx.org/en/docs/http/ngx_http_access_module.html#allow
      - 0.0.0.0/0        # We need to restrict ip access rules for security. 0.0.0.0/0 is for test.
      #admin_listen:
        #ip: 127.0.0.1
        #port: 9180
      #https_admin: true

      #admin_api_mtls:
        #admin_ssl_ca_cert: "/data/certs/mtls_ca.crt"              # Path of your self-signed ca cert.
        #admin_ssl_cert: "/data/certs/mtls_server.crt"             # Path of your self-signed server side cert.
        #admin_ssl_cert_key: "/data/certs/mtls_server.key"

    admin_key:
      - name: "admin"
        key: 8aOWcEqGY4gHwPh2deX6zFSvoLuQKBJk
        role: admin                 # admin: manage all configuration data

      - name: "viewer"
        key: ldWVHLETtSzJmexjQZXkK4uCnrbPGUsY
        role: viewer

  etcd:
    host: # it's possible to define multiple etcd hosts addresses of the same etcd cluster.
      - "http://etcd:2379"          # multiple etcd address
    prefix: "/apisix"               # apisix configurations prefix
    timeout: 30                     # 30 seconds

plugins:
  - real-ip                        # priority: 23000
  - ai                             # priority: 22900
  - client-control                 # priority: 22000
  - proxy-control                  # priority: 21990
  - request-id                     # priority: 12015
  - zipkin                         # priority: 12011
  #- skywalking                    # priority: 12010
  #- opentelemetry                 # priority: 12009
  - ext-plugin-pre-req             # priority: 12000
  - fault-injection                # priority: 11000
  - mocking                        # priority: 10900
  - serverless-pre-function        # priority: 10000
  #- batch-requests                # priority: 4010
  - cors                           # priority: 4000
  - ip-restriction                 # priority: 3000
  - ua-restriction                 # priority: 2999
  - referer-restriction            # priority: 2990
  - csrf                           # priority: 2980
  - uri-blocker                    # priority: 2900
  - request-validation             # priority: 2800
  - openid-connect                 # priority: 2599
  - cas-auth                       # priority: 2597
  - authz-casbin                   # priority: 2560
  - authz-casdoor                  # priority: 2559
  - wolf-rbac                      # priority: 2555
  - ldap-auth                      # priority: 2540
  - hmac-auth                      # priority: 2530
  - basic-auth                     # priority: 2520
  - jwt-auth                       # priority: 2510
  - key-auth                       # priority: 2500
  - consumer-restriction           # priority: 2400
  - forward-auth                   # priority: 2002
  - opa                            # priority: 2001
  - authz-keycloak                 # priority: 2000
  #- error-log-logger              # priority: 1091
  - proxy-cache                    # priority: 1085
  - body-transformer               # priority: 1080
  - proxy-mirror                   # priority: 1010
  - proxy-rewrite                  # priority: 1008
  - asec-source-ip-insert          # priority: 1009
  - workflow                       # priority: 1006
  - api-breaker                    # priority: 1005
  - limit-conn                     # priority: 1003
  - limit-count                    # priority: 1002
  - limit-req                      # priority: 1001
  #- node-status                   # priority: 1000
  - gzip                           # priority: 995
  - server-info                    # priority: 990
  - traffic-split                  # priority: 966
  - redirect                       # priority: 900
  - response-rewrite               # priority: 899
  - degraphql                      # priority: 509
  - kafka-proxy                    # priority: 508
  #- dubbo-proxy                   # priority: 507
  - grpc-transcode                 # priority: 506
  - grpc-web                       # priority: 505
  - public-api                     # priority: 501
  - prometheus                     # priority: 500
  - datadog                        # priority: 495
  - loki-logger                    # priority: 414
  - elasticsearch-logger           # priority: 413
  - echo                           # priority: 412
  - loggly                         # priority: 411
  - http-logger                    # priority: 410
  - splunk-hec-logging             # priority: 409
  - skywalking-logger              # priority: 408
  - google-cloud-logging           # priority: 407
  - sls-logger                     # priority: 406
  - tcp-logger                     # priority: 405
  - kafka-logger                   # priority: 403
  - rocketmq-logger                # priority: 402
  - syslog                         # priority: 401
  - udp-logger                     # priority: 400
  - file-logger                    # priority: 399
  - clickhouse-logger              # priority: 398
  - tencent-cloud-cls              # priority: 397
  - inspect                        # priority: 200
  #- log-rotate                    # priority: 100
  # <- recommend to use priority (0, 100) for your custom plugins
  - example-plugin                 # priority: 0
  #- gm                            # priority: -43
  - aws-lambda                     # priority: -1899
  - azure-functions                # priority: -1900
  - openwhisk                      # priority: -1901
  - openfunction                   # priority: -1902
  - serverless-post-function       # priority: -2000
  - ext-plugin-post-req            # priority: -3000
  - ext-plugin-post-resp           # priority: -4000
  - asec-response-rewrite          # priority: 898
  - asec-virtual-ip                # priority: 1998
  - asec-url-control               # priority: 2010
  - asec-forward-auth              # priority: 2003
  - asec-kafka-logger              # priority: 404
  - asec-response-process          # priority: 888
  - asec-watermark                 # priority: 887
  - asec-form-fill                 # priority: 881
  - asec-identify-sensitive        # priority: 866

ext-plugin:
  cmd: ["/usr/local/apisix/strategy_plugin/go-runner","-c=/usr/local/apisix/strategy_plugin/conf/config.yaml"]
