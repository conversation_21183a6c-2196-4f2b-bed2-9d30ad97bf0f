// 表单代填插件 - APISIX静态资源版本
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        define([], factory());
    } else if (typeof module === 'object' && module.exports) {
        module.exports = factory();
    } else {
        root.FormFill = factory();
    }
}(this, function () {
    var FormFill = {
        State: 0,
        config: {}
    };

    function findElementBySelector(selector) {
        try {
            return document.querySelector(selector);
        } catch (e) {
            return null;
        }
    }

    function findElementByArray(searchArray, elementType) {
        if (!searchArray || !Array.isArray(searchArray)) {
            return null;
        }
        
        var elements = document.querySelectorAll(elementType);
        
        for (var i = 0; i < elements.length; i++) {
            var element = elements[i];
            if (element.offsetParent === null && element.style.display !== 'none') {
                continue;
            }
            
            var elementId = element.id ? element.id.toLowerCase() : '';
            var elementName = element.name ? element.name.toLowerCase() : '';
            var elementClass = element.className ? element.className.toLowerCase() : '';
            var elementValue = element.value ? element.value.toLowerCase() : '';
            var elementPlaceholder = element.placeholder ? element.placeholder.toLowerCase() : '';
            
            for (var j = 0; j < searchArray.length; j++) {
                var searchTerm = searchArray[j].toLowerCase();
                if (elementId.indexOf(searchTerm) !== -1 ||
                    elementName.indexOf(searchTerm) !== -1 ||
                    elementClass.indexOf(searchTerm) !== -1 ||
                    elementValue.indexOf(searchTerm) !== -1 ||
                    elementPlaceholder.indexOf(searchTerm) !== -1) {
                    return element;
                }
            }
        }
        return null;
    }

    function fillInput(element, value) {
        if (!element || !value) return;
        
        element.value = value;
        var events = ['input', 'change', 'blur'];
        events.forEach(function(eventType) {
            var event = new Event(eventType, { bubbles: true });
            element.dispatchEvent(event);
        });
    }

    function getCookie(name) {
        var value = "; " + document.cookie;
        var parts = value.split("; " + name + "=");
        if (parts.length == 2) return parts.pop().split(";").shift();
        return null;
    }

    function addCookie(name, value, seconds) {
        var expires = "";
        if (seconds) {
            var date = new Date();
            date.setTime(date.getTime() + (seconds * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    function customFillForm(settings) {
        var accountInput = null;
        var passwordInput = null;
        var submitButton = null;

        if (settings.AccountInputType === 'fixed' && settings.AccountInputValue) {
            accountInput = findElementBySelector(settings.AccountInputValue);
        } else if (settings.AccountInputType === 'system_try') {
            accountInput = findElementByArray(settings.autoAccountInputArr, 'input');
        }

        if (settings.PwdInputType === 'fixed' && settings.PwdInputValue) {
            passwordInput = findElementBySelector(settings.PwdInputValue);
        } else if (settings.PwdInputType === 'system_try') {
            passwordInput = findElementByArray(settings.autoPwdInputArr, 'input');
        }

        if (settings.SubmitInputType === 'fixed' && settings.SubmitInputValue) {
            submitButton = findElementBySelector(settings.SubmitInputValue);
        } else if (settings.SubmitInputType === 'system_try') {
            submitButton = findElementByArray(settings.autoSubmitInputArr, 'button,input[type="submit"],input[type="button"]');
        }

        var accountFilled = false;
        var passwordFilled = false;
        
        if (accountInput && settings.AccountDataValue) {
            fillInput(accountInput, settings.AccountDataValue);
            accountFilled = true;
        }
        if (passwordInput && settings.PwdDataValue) {
            fillInput(passwordInput, settings.PwdDataValue);
            passwordFilled = true;
        }

        if (submitButton && accountFilled && passwordFilled) {
            setTimeout(function() {
                if (accountInput.value && passwordInput.value) {
                    submitButton.click();
                }
            }, 1000);
        }
    }

    function autoFillForm(settings) {
        var accountInput = findElementByArray(settings.autoAccountInputArr, 'input');
        var passwordInput = findElementByArray(settings.autoPwdInputArr, 'input');
        var submitButton = findElementByArray(settings.autoSubmitInputArr, 'button,input[type="submit"],input[type="button"]');

        var accountFilled = false;
        var passwordFilled = false;
        
        if (accountInput && settings.AccountDataValue) {
            fillInput(accountInput, settings.AccountDataValue);
            accountFilled = true;
        }
        if (passwordInput && settings.PwdDataValue) {
            fillInput(passwordInput, settings.PwdDataValue);
            passwordFilled = true;
        }

        if (!accountInput || !passwordInput || !submitButton) {
            return false;
        }

        if (settings.AutoLogin === '1' && accountFilled && passwordFilled) {
            var autoLoginPed = parseInt(settings.AutoLoginPed) || 300;
            var lastLoginTime = getCookie('asec_last_login_time') || '0';
            var currentTime = Math.floor(Date.now() / 1000);
            
            if (currentTime - parseInt(lastLoginTime) > autoLoginPed) {
                setTimeout(function() {
                    if (accountInput.value && passwordInput.value) {
                        var currentSubmitButton = findElementByArray(settings.autoSubmitInputArr, 'button,input[type="submit"],input[type="button"]');
                        if (currentSubmitButton) {
                            addCookie('asec_last_login_time', currentTime.toString(), autoLoginPed);
                            currentSubmitButton.click();
                        }
                    }
                }, 1000);
            }
        }
        
        return true;
    }

    function loadForm(settings) {
        if (settings.APPSubType === 'custom_fill') {
            customFillForm(settings);
        } else {
            autoFillForm(settings);
        }
        FormFill.State = 1;
        return true;
    }

    function checkURLMatch(loginUrl) {
        if (!loginUrl || loginUrl === "") {
            return true;
        }
        
        var currentUrl = window.location.href;
        
        if (currentUrl.indexOf(loginUrl) !== -1 || loginUrl.indexOf(currentUrl) !== -1) {
            return true;
        }
        
        try {
            var currentPath = new URL(currentUrl).pathname;
            var loginPath = new URL(loginUrl).pathname;
            
            if (currentPath === loginPath) {
                return true;
            }
        } catch (e) {
            // URL解析失败，继续其他检查
        }
        
        return false;
    }

    function pageReady(fn) {
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            setTimeout(fn, 1);
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    }

    FormFill.init = function (settings) {
        FormFill.config = settings || {};
        
        if (!settings || !settings.APPType) {
            return;
        }
        
        if (!checkURLMatch(settings.LoginUrl)) {
            return;
        }
        
        pageReady(function() {
            setTimeout(function() {
                loadForm(FormFill.config);
            }, 1000);
        });
    };

    return FormFill;
}));
