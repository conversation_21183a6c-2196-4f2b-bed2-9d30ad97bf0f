--
-- Licensed to the Apache Software Foundation (ASF) under one or more
-- contributor license agreements.  See the NOTICE file distributed with
-- this work for additional information regarding copyright ownership.
-- The ASF licenses this file to You under the Apache License, Version 2.0
-- (the "License"); you may not use this file except in compliance with
-- the License.  You may obtain a copy of the License at
--
--     http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing, software
-- distributed under the License is distributed on an "AS IS" BASIS,
-- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-- See the License for the specific language governing permissions and
-- limitations under the License.
--

--- Get configuration form ngx.shared.DICT
--
-- @module core.config_xds
local ngx = ngx
local core = require("apisix.core")
local plugin = require("apisix.plugin")
local plugin_name = "asec-identify-sensitive"
local ffi               = require ("ffi")
local bit    = require("bit")
local protoc    = require("protoc")
local pb        = require("pb")
local lfs = require("lfs")
local download_dir = "/usr/local/apisix/download"
local engine_ffi
local file_path
ffi.cdef[[
    // 初始化并且设置配置数据库路径
    bool InitSensitiveEngine(const char* config_db_path, const char* run_dir);

    // 获取文件敏感性
    bool GetFileSensitive(const char* byte_param, char** byte_package);

    // 释放package数组
    void FreeSensitivePackage(char** byte_package);
]]

local schema = {
    type = "object",
    properties = {
        enable = {
            description = "identify sensitive enable config",
            type = "boolean",
            default = false,
        },
        download_conf = {
            type = "object",
            properties = {
                content_type_list = {
                    type = "array",
                    minItems = 1,
                    items = {
                        type = "string",
                        -- "download content type"
                        pattern = "^[^:]+$"
                    }
                }
            }
        }
    }
}

local _M = {
    version = 0.1,
    priority = 866,
    name = plugin_name,
    schema = schema,
}
-- load proto
local pb_state

local function load_proto()
    pb.state(nil)
    protoc.reload()
    pb.option("int64_as_string")
    local pubsub_protoc = protoc.new()
    pubsub_protoc:addpath("apisix/include/apisix/model/proto")
    local ok, err = pcall(pubsub_protoc.loadfile, pubsub_protoc, "sensitive_engine.proto")
    if not ok then
        ngx.log(ngx.ERR, "failed to load protocol: "..err)
        return err
    end
    pb_state = pb.state(nil)
end

function _M.init()
    -- call this function when plugin is loaded
    local attr = plugin.plugin_attr(plugin_name)
    if attr then
        core.log.info(plugin_name, " get plugin attr val: ", attr.val)
    end
    engine_ffi = ffi.load("/usr/local/apisix/apisix/plugins/so_sdk/libAsecSensitiveEngine.so")
    local param = ffi.cast("char *", "/usr/local/apisix/apisix/plugins/so_sdk/asec-client-conf.db")
    local run_dir_param = ffi.cast("char *", "/usr/local/apisix/apisix/plugins/so_sdk")
    engine_ffi.InitSensitiveEngine(param,run_dir_param)
end

-- proto encode
local function encode(data)
    local err = load_proto()
    if err then
        core.log.warn(" load proto err: ", err)
    end
    pb.state(pb_state)
    return pb.encode("SensitiveExtractParam", data)
end

-- proto decode
local function decode(data)
    local err = load_proto()
    if err then
        core.log.warn(" load proto err: ", err)
    end
    pb.state(pb_state)
    return pb.decode("SensitiveExtractPackage", data)
end

local function contains(tbl, value)
    if tbl == nil then return false end
    for _, v in ipairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end


local function get_filename(res)
    local filename = res["Content-Disposition"]
    if filename then
        local m, err = ngx.re.match(filename, "filename=\"(.+)\"")
        if m then
            filename = m[1]
        end
    else
        local m, err = ngx.re.match(ngx.var.uri, "([^/]+)$")
        if m then
            filename = m[1]
        end
    end
    return filename
end

local function bor(arg)
    local result = 0
    for i,v in ipairs(arg) do
        result = result + v - bit.band(result, v)
    end
    return result
end

local function check_enable(conf)
    local content_type = ngx.header['Content-Type']
    if not conf.enable then
        return false
    end
    if contains(conf.download_conf and conf.download_conf.content_type_list, content_type) ~= true then
        return false
    end
    if tonumber(ngx.header['Content-Length']) > 50 * 1024 * 1024 then
        core.log.warn("download more than 50MB!")
        return false
    end
    if tonumber(ngx.header['Content-Length']) == 0  then
        core.log.warn("download file is empty!")
        return false
    end
    return true
end

local function scanSensitive(conf, ctx)
    --if not check_enable(conf) then
    --    return
    --end

    -- 文件敏感识别 param
    local match_type_list = {2,4,8,16}
    local bodyReq = {
        ["match_type"] = bor(match_type_list),
        ["match_mode"] = 1,
        ["file_path"] = file_path
    }
    local bodyReqByte = encode(bodyReq)
    local param = ffi.cast("char *", bodyReqByte)

    local ret = ""
    local ret_param = ffi.new('char[?]', string.len(ret)+1)
    ffi.copy(ret_param, ret)
    local result = ffi.new('char*[1]')

    local check = engine_ffi.GetFileSensitive(param,result)
    if check then
        core.log.warn("check sensitive success!")
        local lua_result = ffi.string(result[0])
        core.log.warn("check c result: ",lua_result)
        -- free
        engine_ffi.FreeSensitivePackage(result)

        local res_data = decode(lua_result)
        local file_category_id = res_data['category_id']
        local stg_list = res_data['sensitive_data_infos']

        local stg_data = {}
        for k, stg_item in pairs(stg_list) do
            if stg_item['hit'] then
                local stg = {
                    ['sensitive_level'] = stg_item['sensitive_data_level'],
                    ['sensitive_id'] = stg_item['sensitive_data_id'],
                    ['sensitive_rule_name'] = stg_item['sensitive_data_name'],
                    ['sensitive_category_id'] = stg_item['sensitive_data_category_id']
                }
                table.insert(stg_data,stg)
            end
        end
        local stg_info = {
            ['sensitive_info'] = stg_data
        }
        -- register apisix var
        core.ctx.register_var("asec_activity", function(ctx)
            return "download"
        end)
        core.ctx.register_var("asec_sensitive_info", function(ctx)
            return core.json.encode(stg_info)
        end)
        core.ctx.register_var("asec_category_id", function(ctx)
            return file_category_id
        end)
    end
end

function _M.body_filter(conf, ctx)
    if not check_enable(conf) then
        return
    end
    -- get resp body
    local body
    local res = ngx.resp.get_headers()
    local filename = get_filename(res)
    local decode_filename = ngx.unescape_uri(filename)
    core.ctx.register_var("asec_file_name", function(ctx)
        return decode_filename
    end)
    body = core.response.hold_body_chunk(ctx)
    if not body then
        return
    end

    -- file retain
    local date = ctx.var.Asec_Access_Time:match("%S+"):gsub("(%s+)"," "):gsub("%-", "")
    local retain_dir = download_dir .. "/".. date.."_"..core.request.header(ctx, 'X-Request-Id')
    file_path = retain_dir .. "/" .. decode_filename
    core.log.warn("file_path: ",file_path)
    lfs.mkdir(retain_dir)
    local file = assert(io.open(file_path, "w+"))
    if not file then
        core.log.err("fail to open file: ",error)
    end

    file:write(body)
    file:close()
    ngx.arg[1] = body
    scanSensitive(conf,ctx)
    return
end


-- #END
return _M
