FROM registry.cn-guangzhou.aliyuncs.com/asdsec/asec_base:v1.0

COPY ./config /usr/local/asec/tunsrv/config
COPY tunsrv /usr/local/asec/tunsrv

RUN mkdir /usr/local/asec/tunsrv/logs
RUN echo "100 vip_return" >> /etc/iproute2/rt_tables

WORKDIR /usr/local/asec/tunsrv
VOLUME /data/conf

ARG HOST_IP
ENV HOST_IP=${HOST_IP}
ENV TZ=Asia/Shanghai

RUN dpkg -i /usr/local/asec/tunsrv/config/iptables/*.deb || apt-get install -f -y 
RUN dpkg -i /usr/local/asec/tunsrv/config/iproutes/*.deb || apt-get install -f -y

#CMD [ "/usr/local/asec/tunsrv/tunsrv", "-config", "/usr/local/asec/tunsrv/config/config.json","-sc","/usr/local/asec/tunsrv/config/sidecar.yaml","-mode","3","-code","#code#" ]

#运行 docker run -p 8000:8000  -v /tmp/asec_se:/etc/tun_server 783830f98268