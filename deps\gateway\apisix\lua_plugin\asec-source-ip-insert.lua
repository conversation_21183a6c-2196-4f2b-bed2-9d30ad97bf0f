local core = require("apisix.core")
local ngx = ngx
local string = string
local tonumber = tonumber

local plugin_name = "asec-source-ip-insert"

local schema = {
    type = "object",
    properties = {
        header = {
            type = "string",
            description = "目标请求头名称",
            default = "X-Real-IP"
        },
        direction = {
            type = "string",
            enum = {"left", "right"},
            description = "插入方向：left从左到右，right从右到左",
            default = "left"
        },
        separator = {
            type = "string",
            description = "分隔符",
            default = ","
        },
        position = {
            type = "integer",
            minimum = 1,
            maximum = 100,
            description = "插入位置（第几个）",
            default = 1
        }
    },
    required = {"header"}
}

local _M = {
    version = 0.1,
    priority = 1009,  -- 在 proxy-rewrite 之前执行
    name = plugin_name,
    schema = schema,
}

-- 检查配置
function _M.check_schema(conf)
    return core.schema.check(schema, conf)
end

-- 分割字符串
local function split_string(str, sep)
    if not str or str == "" then
        return {}
    end
    
    local result = {}
    local pattern = "([^" .. sep .. "]+)"
    for match in string.gmatch(str, pattern) do
        local trimmed = string.gsub(match, "^%s*(.-)%s*$", "%1")  -- 去除首尾空格
        if trimmed ~= "" then
            table.insert(result, trimmed)
        end
    end
    return result
end

-- 在指定位置插入IP
local function insert_ip_at_position(existing_ips, new_ip, position, direction, separator)
    if not existing_ips or existing_ips == "" then
        return new_ip
    end
    
    local ip_list = split_string(existing_ips, separator)
    
    if direction == "right" then
        -- 从右到左插入（从末尾开始计算位置）
        -- position=1表示倒数第1个位置，position=2表示倒数第2个位置
        if position < 1 or position > #ip_list then
            -- 如果位置无效或超出范围，插入到开头
            table.insert(ip_list, 1, new_ip)
        else
            local insert_pos = #ip_list - position + 1
            table.insert(ip_list, insert_pos + 1, new_ip)
        end
    else
        -- 从左到右插入（默认，从开头开始计算位置）
        local insert_pos = position
        if insert_pos < 1 then
            insert_pos = 1
        elseif insert_pos > #ip_list + 1 then
            insert_pos = #ip_list + 1
        end
        table.insert(ip_list, insert_pos, new_ip)
    end
    
    return table.concat(ip_list, separator)
end

-- 主要的重写逻辑
function _M.rewrite(conf, ctx)
    -- 获取客户端真实IP地址
    local client_ip = ngx.var.remote_addr
    if not client_ip then
        core.log.warn("asec-source-ip-insert: 无法获取客户端IP")
        return
    end
    
    local existing_header = core.request.header(ctx, conf.header)
    local separator = conf.separator or ","
    
    local new_value = insert_ip_at_position(
        existing_header, 
        client_ip, 
        conf.position or 1, 
        conf.direction or "left",
        separator
    )
    
    -- 设置新的请求头
    core.request.set_header(ctx, conf.header, new_value)
    
    core.log.debug("asec-source-ip-insert: 设置请求头 ", conf.header, " = ", new_value,
                  " (原值: ", existing_header or "空", ", 客户端IP: ", client_ip, ")")
end

return _M
