local _M = {}

function _M.rewrite(conf, ctx)
    local host = ngx.var.host
    local base_domain = conf.base_domain or "test.com"
    
    -- 匹配 IP 格式: 192-168-1-100.domain.com 或 192-168-1-100-8080-p.domain.com
    local pattern = "^([^%.]+)%." .. base_domain:gsub("%%.", "%%%.") .. "$"
    local m = ngx.re.match(host, pattern, "jo")
    
    if m then
        local encoded = m[1]
        local ip_parts = {}
        local port = 80
        local protocol = "http"
        
        -- 检查是否有端口信息
        if encoded:find("%-p$") then
            -- 格式: 192-168-1-100-8080-p
            local without_p = encoded:gsub("%-p$", "")
            local last_dash = without_p:find("%-[^%-]*$")
            if last_dash then
                port = tonumber(without_p:sub(last_dash + 1)) or 80
                encoded = without_p:sub(1, last_dash - 1)
            end
        end
        
        -- 检查是否是HTTPS (以-s结尾)
        if encoded:find("%-s$") then
            protocol = "https"
            encoded = encoded:gsub("%-s$", "")
            if port == 80 then port = 443 end
        end
        
        -- 解析IP地址
        for part in encoded:gmatch("([^%-]+)") do
            local num = tonumber(part)
            if num and num >= 0 and num <= 255 then
                table.insert(ip_parts, part)
            end
        end
        
        if #ip_parts == 4 then
            local ip = table.concat(ip_parts, ".")
            ngx.var.upstream_host = ip
            ngx.var.upstream_port = port
            ngx.var.upstream_scheme = protocol
            ngx.log(ngx.INFO, "Dynamic upstream: " .. ip .. ":" .. port .. " (" .. protocol .. ")")
        else
            ngx.log(ngx.ERR, "Invalid IP format in host: " .. host)
        end
    else
        ngx.log(ngx.WARN, "Host pattern not matched: " .. host)
    end
end

return _M