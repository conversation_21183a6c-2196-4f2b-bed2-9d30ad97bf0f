#!/bin/bash

set -e


INSTALL_DIR=/etc/tun_server

current_dir=$(cd "$(dirname "$0")"; pwd)
current_filepath="$current_dir/$(basename "$0")"

cmd_list=$@

ARGS=$(getopt -o s:p:h:e:c: --long se,platform,help,env_file:code: -- "$@")
eval set -- "$ARGS"

se_addr=""
platform_addr=""
env_file_path=""

while true; do
    case $1 in
        -s|--se)
            se_addr=$2
            shift 2
            ;;
        -p|--platform)
            platform_addr=$2
            shift 2
            ;;
        -e|--env_file)
            env_file_path=$2
            shift 2
            ;;
        -c|--code)
            gateway_code=$2
            shift 2
            ;;
        -h|--help)
            cat $current_dir/connector-usage
            exit 0
            ;;
        --)
            shift
            break
            ;;
        *)
            shift
            ;;
    esac
done

if [ -z $env_file_path ];then
    echo "please use the option -e/--env_file to provide the path of the environment file."
    exit -1
else
    source $env_file_path
fi

if [ -z "$se_addr" ];then
    echo "please use the -s/--se option to set the gateway address."
    exit -1
fi

if [ -z "$platform_addr" ];then
    echo "please use the -p/--platform option to set the platform address."
    exit -1 
fi

if systemctl status tunsrv-connector &>/dev/null; then
    echo "service tunsrv-connector.servicce already exists."
fi

set +e
if [ ! -x $current_dir/init.sh ];then
    chmod +x $current_dir/init.sh
fi
$current_dir/init.sh
if [ $? != 0 ]; then
    echo "restart"
    sleep 1
    $current_filepath $cmd_list
fi
set -e

if [ ! -d ${INSTALL_DIR} ];then
    mkdir -p ${INSTALL_DIR}
fi

if [ ! -f ${INSTALL_DIR}/tunsrv ];then
    cp $current_dir/tunsrv ${INSTALL_DIR}/tunsrv
fi

if [ ! -x ${INSTALL_DIR}/tunsrv ];then
    chmod +x ${INSTALL_DIR}/tunsrv
fi

if [ ! -f ${INSTALL_DIR}/config/server.crt ];then
    cp -r $current_dir/config/server.crt ${INSTALL_DIR}
fi

if [ ! -f ${INSTALL_DIR}/config/server.key ];then
    cp -r $current_dir/config/server.key ${INSTALL_DIR}
fi

if [ ! -f ${INSTALL_DIR}/config/sidecar.yaml ];then
    cp -r $current_dir/config/sidecar.yaml ${INSTALL_DIR}
    sed "s/#platform_address#/${platform_addr}/g" -i ${INSTALL_DIR}/sidecar.yaml
fi

if [ ! -f ${INSTALL_DIR}/config/connector.json ];then
    cp -r $current_dir/config/connector.json ${INSTALL_DIR}
    sed "s/#se_address#/$se_addr/g" -i ${INSTALL_DIR}/connector.json
fi


if [ ! -x /usr/lib/systemd/system/tunsrv-connector.service ];then
    cp $current_dir/tunsrv-connector.service /usr/lib/systemd/system/tunsrv-connector.service
    sed "s/#code#/${gateway_code}/g" -i "/usr/lib/systemd/system/tunsrv-connector.service"
fi

systemctl daemon-reload
systemctl enable tunsrv-connector
systemctl start tunsrv-connector



if systemctl is-active --quiet tunsrv-connector.service; then
  echo "tunsrv-connector.service installed success!"
else
  echo "tunsrv-connector.service installation failed"
  exit -1
fi

systemctl status tunsrv-connector
