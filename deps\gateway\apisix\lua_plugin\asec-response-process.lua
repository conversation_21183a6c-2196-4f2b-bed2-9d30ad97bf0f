--
-- Licensed to the Apache Software Foundation (ASF) under one or more
-- contributor license agreements.  See the NOTICE file distributed with
-- this work for additional information regarding copyright ownership.
-- The ASF licenses this file to You under the Apache License, Version 2.0
-- (the "License"); you may not use this file except in compliance with
-- the License.  You may obtain a copy of the License at
--
--     http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing, software
-- distributed under the License is distributed on an "AS IS" BASIS,
-- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-- See the License for the specific language governing permissions and
-- limitations under the License.
--
local core        = require("apisix.core")
local expr        = require("resty.expr.v1")
local re_compile  = require("resty.core.regex").re_match_compile
local plugin_name = "asec-response-process"
local ngx         = ngx
local re_match    = ngx.re.match
local re_sub      = ngx.re.sub
local re_gsub     = ngx.re.gsub
local pairs       = pairs
local ipairs      = ipairs
local type        = type
local pcall       = pcall


local lrucache = core.lrucache.new({
    type = "plugin",
})

local schema = {
}


local _M = {
    version  = 0.1,
    priority = 888,
    name     = plugin_name,
    schema   = schema,
}

do
    function _M.header_filter(ctx)
        local timestamp = os.time()
        local status = ngx.status
        local app_name = ngx.header['app_name']
        local app_id = ngx.header['app_id']
        local action = ngx.header['action']
        local strategy_name = ngx.header['strategy_name']
        local strategy_id = ngx.header['strategy_id']
        local app_type = ngx.header['app_type']
        local server_addr = ngx.header['server_addr']
        if tonumber(status) < 400 then
            app_name = core.request.header(ctx, 'app_name')
            app_id = core.request.header(ctx, 'app_id')
            action = core.request.header(ctx, 'action')
            strategy_name = core.request.header(ctx, 'strategy_name')
            strategy_id = core.request.header(ctx, 'strategy_id')
            app_type = core.request.header(ctx, 'app_type')
            server_addr = core.request.header(ctx, 'server_addr')
        end

        core.log.warn("status code: ",ngx.status," app_name: ",app_name)

        core.ctx.register_var("Asec_Server_Addr", function(ctx)
            return server_addr
        end)
        core.ctx.register_var("Asec_App_Type", function(ctx)
            return app_type
        end)
        core.ctx.register_var("Asec_App_Name", function(ctx)
            return app_name
        end)
        core.ctx.register_var("Asec_App_Id", function(ctx)
            return app_id
        end)
        core.ctx.register_var("Asec_Verify", function(ctx)
            return ngx.header['Auth-Verify']
        end)
        core.ctx.register_var("Asec_Strategy_Action", function(ctx)
            return action
        end)
        core.ctx.register_var("Asec_Strategy_Name", function(ctx)
            return strategy_name
        end)
        core.ctx.register_var("Asec_Strategy_Id", function(ctx)
            return strategy_id
        end)

        local date = os.date("%Y-%m-%d %H:%M:%S", timestamp)
        core.ctx.register_var("Asec_Access_Time", function(ctx)
            return date
        end)

        -- response header
        core.ctx.register_var("rsp_cache_control", function(ctx)
            return ngx.header['Cache-Control']
        end)

        core.ctx.register_var("rsp_content_disposition", function(ctx)
            return ngx.header['Content-Disposition']
        end)

        core.ctx.register_var("rsp_content_encoding", function(ctx)
            return ngx.header['Content-Encoding']
        end)

        core.ctx.register_var("rsp_content_length", function(ctx)
            return ngx.header['Content-Length']
        end)

        core.ctx.register_var("rsp_content_type", function(ctx)
            return ngx.header['Content-Type']
        end)

        core.ctx.register_var("rsp_cookie", function(ctx)
            return ngx.header['Cookie']
        end)

        core.ctx.register_var("rsp_last_modified", function(ctx)
            return ngx.header['Last-Modified']
        end)

        core.ctx.register_var("rsp_set_cookie", function(ctx)
            if type(ngx.header['Set-Cookie']) == "table" then
                return ngx.header['Set-Cookie'][1]
            end
            return ngx.header['Set-Cookie']
        end)

        core.ctx.register_var("rsp_transfer_encoding", function(ctx)
            return ngx.header['Transfer-Encoding']
        end)

    end

end  -- do


return _M
