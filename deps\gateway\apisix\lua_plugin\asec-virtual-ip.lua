local core = require("apisix.core")
local http = require("resty.http")
local cjson = require("cjson")
local ngx = ngx

local plugin_name = "asec-virtual-ip"

local schema = {
    type = "object",
    properties = {
        virtual_ip_service_host = {
            type = "string",
            default = "asec_virtual_ip_host"
        },
        virtual_ip_service_port = {
            type = "integer",
            minimum = 1,
            maximum = 65535,
            default = 50015
        },
        timeout = {
            type = "integer",
            minimum = 1,
            maximum = 60000,
            default = 3000
        },
        exclude_paths = {
            type = "array",
            items = {
                type = "string"
            },
            default = {
                "/api/v1/user/login",
                "/api/v1/user/register", 
                "/api/v1/auth",
                "/login",
                "/register",
                "/favicon.ico"
            }
        }
    }
}

local _M = {
    version = 0.1,
    priority = 1998,
    name = plugin_name,
    schema = schema,
}

function _M.check_schema(conf)
    return core.schema.check(schema, conf)
end

-- 获取用户的虚拟IP
local function get_or_check_virtual_ip(conf, user_id, user_name, client_id)
    local httpc = http.new()
    httpc:set_timeout(conf.timeout)
    
    local service_url = "http://" .. conf.virtual_ip_service_host .. ":" .. conf.virtual_ip_service_port

    core.log.debug("Requesting virtual IP for user ", user_id, " from service: ", service_url)

    -- 1. 首先检查用户是否已有虚拟IP
    local check_res, check_err = httpc:request_uri(
        service_url .. "/api/virtual-ip/user-ip?user_id=" .. user_id, {
        method = "GET",
        headers = {
            ["Content-Type"] = "application/json",
            ["X-API-Key"] = "c34ff517ed828d279f91c884caac8f1be1efb999"
        }
    })
    
    if check_res and check_res.status == 200 then
        local check_result = cjson.decode(check_res.body)
        if check_result.found then
            core.log.debug("User ", user_id, " already has virtual IP: ", check_result.virtual_ip)
            return check_result.virtual_ip
        else
            core.log.debug("User ", user_id, " has no existing virtual IP, proceeding to allocate")
        end
    else
        core.log.warn("Failed to check existing virtual IP for user ", user_id, 
                    ", status: ", check_res and check_res.status or "nil", 
                    ", error: ", check_err or "none")
    end
    
    -- 2. 分配新的虚拟IP
    local req_body = {
        user_id = user_id,
        user_name = user_name,
        client_id = client_id,
        source_type = "web_gateway"
    }

    core.log.debug("Allocating new virtual IP for user ", user_id, " with request: ", cjson.encode(req_body))
    
    local alloc_res, alloc_err = httpc:request_uri(
        service_url .. "/api/virtual-ip/allocate", {
        method = "POST",
        body = cjson.encode(req_body),
        headers = {
            ["Content-Type"] = "application/json",
            ["X-API-Key"] = "c34ff517ed828d279f91c884caac8f1be1efb999"
        }
    })
    
    if not alloc_res then
        core.log.warn("Failed to request virtual IP allocation for user ", user_id, ": ", alloc_err)
        return nil
    end
    
    if alloc_res.status ~= 200 then
        core.log.warn("Virtual IP allocation failed for user ", user_id, 
                        " - status: ", alloc_res.status, ", body: ", alloc_res.body)
        return nil
    end
    
    local result = cjson.decode(alloc_res.body)
    if result.success then
        core.log.info("Successfully allocated virtual IP ", result.virtual_ip, " to user ", user_id)
        return result.virtual_ip
    else
        core.log.warn("Virtual IP allocation unsuccessful for user ", user_id, 
                        " - message: ", result.message or "Unknown error")
        return nil
    end
end

function _M.access(conf, ctx)
    -- 获取请求路径
    local uri = ngx.var.uri
    
    -- 检查是否在排除列表中
    if conf.exclude_paths then
        for _, exclude_path in ipairs(conf.exclude_paths) do
            if string.find(uri, exclude_path, 1, true) then
                core.log.debug("Skipping virtual IP allocation for excluded path: ", uri)
                return
            end
        end
    end
    
    -- 获取用户信息
    local user_id = core.request.header(ctx, "Asec_User_Id")
    local user_name = core.request.header(ctx, "Asec_User_Name")  
    local client_id = core.request.header(ctx, "Asec_Client_Id")
    
    core.log.debug("Virtual IP plugin processing request - URI: ", uri, 
                    ", UserID: ", user_id or "nil", 
                    ", UserName: ", user_name or "nil", 
                    ", ClientID: ", client_id or "nil")
    
    if not user_id then
        core.log.debug("No user ID found for URI: ", uri, " - skipping virtual IP allocation")
        return
    end
    
    -- 获取或检查虚拟IP
    local virtual_ip = get_or_check_virtual_ip(conf, user_id, user_name, client_id)
    if virtual_ip then
        -- 1. 设置自定义虚拟IP头部
        core.request.set_header(ctx, "X-Virtual-IP", virtual_ip)
        
        -- 2. 将虚拟IP添加到 X-Forwarded-For 头中
        local existing_xff = core.request.header(ctx, "X-Forwarded-For")
        local new_xff
        
        if existing_xff then
            -- 如果已有 X-Forwarded-For，将虚拟IP追加到末尾
            new_xff = existing_xff .. ", " .. virtual_ip
        else
            -- 如果没有 X-Forwarded-For，直接设置虚拟IP
            new_xff = virtual_ip
        end
        
        core.request.set_header(ctx, "X-Forwarded-For", new_xff)
        
        core.log.info("Successfully applied virtual IP ", virtual_ip, " for user ", user_id,
                        " - X-Forwarded-For: ", new_xff)
    else
        core.log.error("Failed to obtain virtual IP for user ", user_id)
    end
end

return _M