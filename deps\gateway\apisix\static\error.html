<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 404错误</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100%;
            height: 100vh;
            background-color: #f4f4f4;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑", "HarmonyOS_Medium", "Helvetica Neue", sans-serif;
        }

        .error-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .error-icon-container {
            margin-bottom: 30px;
        }

        .error-icon {
            width: 80px;
            height: 80px;
            fill: #FF4D4D;
        }

        .main-error-message {
            font-size: 18px;
            color: #333;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 20px;
        }

        .error-reason-display {
            margin-bottom: 30px;
            text-align: left;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .reason-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .reason-detail {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        @media (max-width: 600px) {
            .error-container {
                padding: 30px 20px;
            }
            
            .main-error-message {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-content">
            <div class="error-icon-container">
                <svg class="error-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                    <path d="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0zM297.6 297.6c-12.8 12.8-12.8 33.472 0 46.272L465.664 512 297.6 680.064a32.768 32.768 0 0 0 46.272 46.336L512 558.272l168.064 168.128a32.704 32.704 0 1 0 46.336-46.336L558.336 512.064l168.128-168.128a32.768 32.768 0 0 0-46.336-46.272L512.064 465.728 343.872 297.6c-12.8-12.8-33.472-12.8-46.272 0z" />
                </svg>
            </div>

            <!-- 错误信息展示 -->
            <div class="error-info-display">
                <div class="main-error-message">
                    很抱歉，您访问的页面不存在，错误代码：404
                </div>
            </div>
            <!-- 错误原因 -->
            <div class="error-reason-display">
                <div class="reason-title">访问阻断：</div>
                <div class="reason-detail">您请求的应用资源不存在或已被移动，请联系系统管理员或技术支持人员处理。</div>            </div>
        </div>
    </div>
</body>
</html>