--
-- ASEC URL访问控制插件
-- 优先级高于asec-forward-auth，专门处理免认证和禁止访问逻辑
--

local core = require("apisix.core")
local ngx = ngx

local plugin_name = "asec-url-control"

local schema = {
    type = "object",
    properties = {
        free_auth_paths = {
            type = "string",
            description = "newline-separated list of paths that bypass authentication"
        },
        forbid_access_paths = {
            type = "string",
            description = "newline-separated list of paths that forbid access"
        },
    }
}

local _M = {
    version = 0.1,
    priority = 2010,  -- 比asec-forward-auth(2003)优先级更高
    name = plugin_name,
    schema = schema,
}

function _M.check_schema(conf)
    return core.schema.check(schema, conf)
end

-- URI匹配函数，支持通配符和复杂模式匹配
local function isMatchUri(org_uri, patternStr)
    if org_uri == nil or patternStr == nil or patternStr == "" then
        return false
    end
    
    -- 分割区间字符串
    local ranges = {}
    for range in string.gmatch(patternStr, '[^\n]+') do
        table.insert(ranges, range)
    end
    
    -- 遍历每个区间
    for _, value in ipairs(ranges) do
        -- 转义特殊字符，但保留通配符
        value = string.gsub(value, "%%", "%%%%")
        value = string.gsub(value, "%.", "%%.")
        value = string.gsub(value, "%-", "%%-")
        value = string.gsub(value, "%+", "%%+")
        value = string.gsub(value, "%^", "%%^")
        value = string.gsub(value, "%$", "%%$")
        value = string.gsub(value, "%(", "%%(")
        value = string.gsub(value, "%)", "%%)")
        value = string.gsub(value, "%[", "%%[")
        value = string.gsub(value, "%]", "%%]")
        value = string.gsub(value, "%{", "%%{")
        value = string.gsub(value, "%}", "%%}")
        
        -- 处理通配符：* 表示任意字符0次或多次，? 表示任意字符1次  
        value = string.gsub(value, "%*", ".*")
        value = string.gsub(value, "%?", ".")
        
        -- 使用完全匹配模式
        local pattern = "^" .. value .. "$"
        
        -- 使用 string.find 进行匹配
        if string.find(org_uri, pattern) then
            core.log.debug("URI match successful: ", org_uri, " matched pattern: ", pattern)
            return true
        end
    end

    return false
end

-- 检查路径是否匹配免认证列表
local function is_free_auth_path(request_path, free_auth_paths)
    return isMatchUri(request_path, free_auth_paths)
end

-- 检查路径是否匹配禁止访问列表
local function is_forbid_access_path(request_path, forbid_access_paths)
    return isMatchUri(request_path, forbid_access_paths)
end

-- 设置虚拟用户信息（用于免认证访问）
local function set_virtual_user_header(ctx)
    core.request.set_header(ctx, 'Asec_User_Id', 'virtual_user')
    core.request.set_header(ctx, 'Asec_User_Name', 'Virtual User')
    core.request.set_header(ctx, 'Asec_Free_Auth', '1') -- 免认证标识
    core.request.set_header(ctx, 'Asec_Sms_Idp', '')
    core.request.set_header(ctx, 'Asec_Group_Id', '')
    core.request.set_header(ctx, 'Asec_User_Identifier', 'virtual_user')
    
    -- core.log.warn("asec-url-control: set virtual user for free auth access, path: ", ctx.var.request_uri)
end

-- 设置禁止访问标识和虚拟用户信息（用于strategy_plugin识别）
local function set_forbid_access_header(ctx)
    core.request.set_header(ctx, 'Asec_Forbid_Access', '1') -- 禁止访问标识
    
    -- 禁止访问时也设置虚拟用户头部，确保后续插件能识别
    core.request.set_header(ctx, 'Asec_User_Id', 'virtual_user')
    core.request.set_header(ctx, 'Asec_User_Name', 'Virtual User')
    core.request.set_header(ctx, 'Asec_Free_Auth', '0') -- 非免认证标识
    core.request.set_header(ctx, 'Asec_Sms_Idp', '')
    core.request.set_header(ctx, 'Asec_Group_Id', '')
    core.request.set_header(ctx, 'Asec_User_Identifier', 'virtual_user')
    
    core.log.warn("asec-url-control: set forbid access flag and virtual user headers, path: ", ctx.var.request_uri)
end

function _M.access(conf, ctx)
    local request_path = ctx.var.request_uri
    
    -- core.log.warn("=== asec-url-control ACCESS START ===")
    -- core.log.warn("request_path=", request_path)
    -- core.log.warn("free_auth_paths=", conf.free_auth_paths, " (type: ", type(conf.free_auth_paths), ")")
    -- core.log.warn("forbid_access_paths=", conf.forbid_access_paths, " (type: ", type(conf.forbid_access_paths), ")")
    
    -- 优先检查禁止访问路径
    if conf.forbid_access_paths and is_forbid_access_path(request_path, conf.forbid_access_paths) then
        core.log.warn("asec-url-control: forbid access path matched: ", request_path)
        set_forbid_access_header(ctx)
        -- 设置处理标记（禁止访问）
        core.request.set_header(ctx, 'Asec_URL_Control_Processed', 'forbid_access')
        return -- 继续执行后续插件，由strategy_plugin统一处理403和日志
    end
    
    -- 检查是否为免认证路径
    if conf.free_auth_paths and is_free_auth_path(request_path, conf.free_auth_paths) then
        core.log.warn("asec-url-control: free auth path matched: ", request_path)
        set_virtual_user_header(ctx)
        -- 设置一个特殊标记，让asec-forward-auth知道已经处理过了（免认证）
        core.request.set_header(ctx, 'Asec_URL_Control_Processed', 'free_auth')
        return -- 继续执行后续插件，但asec-forward-auth会跳过认证
    end
    
    -- core.log.warn("asec-url-control: no URL control rules matched, path: ", request_path)
    -- core.log.warn("=== asec-url-control ACCESS END ===")
end

return _M
