--
-- Licensed to the Apache Software Foundation (ASF) under one or more
-- contributor license agreements.  See the NOTICE file distributed with
-- this work for additional information regarding copyright ownership.
-- The ASF licenses this file to You under the Apache License, Version 2.0
-- (the "License"); you may not use this file except in compliance with
-- the License.  You may obtain a copy of the License at
--
--     http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing, software
-- distributed under the License is distributed on an "AS IS" BASIS,
-- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-- See the License for the specific language governing permissions and
-- limitations under the License.
--
local core        = require("apisix.core")
local expr        = require("resty.expr.v1")
local re_compile  = require("resty.core.regex").re_match_compile
local plugin_name = "asec-watermark"
local ngx         = ngx
local re_match    = ngx.re.match
local re_sub      = ngx.re.sub
local re_gsub     = ngx.re.gsub
local pairs       = pairs
local ipairs      = ipairs
local type        = type
local pcall       = pcall
local body_length
local user_id


local schema = {
    type = "object",
    properties = {
        enable = {
            description = "watermark enable config",
            type = "boolean",
            default = false,
        },
        watermark_conf = {
            type = "object",
            properties = {
                content = {
                    type = "array",
                    minItems = 1,
                    items = {
                        type = "string",
                        -- "user_name"
                        pattern = "^[^:]+$"
                    }
                },
                alpha = {
                    description = "watermark config transparency",
                    type = "integer",
                    minimum = 0,
                    maximum = 100,
                    default = 80,
                },
                color = {
                    description = "watermark color",
                    type = "string",
                    default = "#BBBBBB",
                },
                columns_spacing = {
                    description = "watermark column spacing",
                    type = "integer",
                    minimum = 40,
                    maximum = 400,
                    default = 40,
                },
                line_spacing = {
                    description = "watermark line spacing",
                    type = "integer",
                    minimum = 40,
                    maximum = 400,
                    default = 40,
                },
                rotate = {
                    description = "watermark rotation angle",
                    type = "integer",
                    minimum = -90,
                    maximum = 90,
                    default = 25,
                },
                size = {
                    description = "watermark font size",
                    type = "integer",
                    minimum = 10,
                    maximum = 50,
                    default = 15,
                },
                secret = {
                    description = "dark watermark enable",
                    type = "boolean",
                    default = false,
                },
                effective_apps = {
                    enable_all = {
                        description = "effective all user config",
                        type = "boolean",
                        default = false,
                    },
                    app_ids = {
                        type = "array",
                        minItems = 1,
                        items = {
                            type = "string",
                            -- "user_name"
                            pattern = "^[^:]+$"
                        }
                    }
                },
                effective_users = {
                    enable_all = {
                        description = "effective all user config",
                        type = "boolean",
                        default = false,
                    },
                    user_ids = {
                        type = "array",
                        minItems = 1,
                        items = {
                            type = "string",
                            -- "user_name"
                            pattern = "^[^:]+$"
                        }
                    },
                    group_ids = {
                        type = "array",
                        minItems = 1,
                        items = {
                            type = "string",
                            -- "user_name"
                            pattern = "^[^:]+$"
                        }
                    }
                },
                exclude_users = {
                    user_ids = {
                        type = "array",
                        minItems = 1,
                        items = {
                            type = "string",
                            -- "user_name"
                            pattern = "^[^:]+$"
                        }
                    },
                    group_ids = {
                        type = "array",
                        minItems = 1,
                        items = {
                            type = "string",
                            -- "user_name"
                            pattern = "^[^:]+$"
                        }
                    },
                    enable_all = {
                        description = "effective all user config",
                        type = "boolean",
                        default = false,
                    },
                }
            }
        },
    },
    dependencies = {
        body = {
            ["not"] = {required = {"filters"}}
        },
        filters = {
            ["not"] = {required = {"body"}}
        }
    }
}


local _M = {
    version  = 0.1,
    priority = 887,
    name     = plugin_name,
    schema   = schema,
}

local function contains(tbl, value)
    if tbl == nil or tbl == ngx.null then return false end
    for _, v in ipairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end

local function get_time()
    -- 截取字符串
    local time = ngx.var.time_iso8601
    local tz = time:sub(math.max(1, #time-5), #time)
    if tz == "+00:00" then
        -- 从UTC转本地时间
        return os.time()+60*60*8
    end
    return os.time()
end

local function get_color(color,alpha)
    local r = tonumber(color:sub(2,3), 16)
    local g = tonumber(color:sub(4,5), 16)
    local b = tonumber(color:sub(6,7), 16)
    local rgb = "'rgba(%d,%d,%d,%s)'"
    local result = (100-alpha)/100
    local final_color = rgb:format(r,g,b,result)
    return final_color
end

local function get_watermark_conf(conf,ctx)
    local watermark_conf_str = [[WaterMark.page({ targe: document.body,
                                                  text: "%s",
                                                  color: %s,
                                                  fontSize: %d,
                                                  cSpace: %d,
                                                  vSpace: %d,
                                                  angle: %d,
                                                  secret: %s
                                })]]
    local watermark_conf = conf.watermark_conf
    local content_tab = watermark_conf.content
    local content = ""

    for _, v in ipairs(watermark_conf.content) do
        core.log.warn("conf content: ",v)
    end
    core.log.warn("Phone : ",ngx.var.http_Asec_full_phone_number," Email: ",ngx.var.http_Asec_full_email)
    if contains(content_tab,"user_name") then
        if ngx.var.http_Asec_User_Name ~= nil then
            content = content .. ngx.var.http_Asec_User_Name
        end
    end
    if contains(content_tab,"last_phone_number")  then
        local last_phone_number = ngx.var.http_Asec_last_phone_number
        if last_phone_number ~= nil then
            content = content .. " " .. last_phone_number
        end
    end
    if contains(content_tab,"time")  then
        local date = os.date("%Y-%m-%d %H:%M", get_time())
        if date ~= nil then
            content = content .. " " .. date
        end
    end
    if contains(content_tab,"full_phone_number")  then
        local full_phone_number = ngx.var.http_Asec_full_phone_number
        if full_phone_number ~= nil then
            content = content .. " " .. full_phone_number
        end
    end
    if contains(content_tab,"prefix_email")  then
        local prefix_email = ngx.var.http_Asec_prefix_email
        if prefix_email ~= nil then
            content = content .. " " .. prefix_email
        end
    end
    if contains(content_tab,"full_email")  then
        local full_email = ngx.var.http_Asec_full_email
        if full_email ~= nil then
            content = content .. " " .. full_email
        end
    end
    local final_color = get_color(watermark_conf.color,watermark_conf.alpha)
    core.log.warn(
            "content: ",content,
            " color:",final_color,
            " font size: ", watermark_conf.size,
            " column_space: ",watermark_conf.columns_spacing,
            " line_spacing: ",watermark_conf.line_spacing,
            " angle: ",watermark_conf.rotate,
            " secret: ",tostring(watermark_conf.secret))
    watermark_conf_str = watermark_conf_str:format(
            content,
            final_color,
            watermark_conf.size,
            watermark_conf.columns_spacing,
            watermark_conf.line_spacing,
            watermark_conf.rotate,
            tostring(watermark_conf.secret)
    )
    return watermark_conf_str

end

local function check_watermark_user(conf)
    user_id = ngx.var.http_Asec_User_Id
    local group_id = ngx.var.http_Asec_Group_Id
    local watermark_conf = conf.watermark_conf
    local effective_users = watermark_conf.effective_users
    local exclude_users = watermark_conf.exclude_users
    if exclude_users ~= nil and exclude_users.enable_all then return false end

    if contains(exclude_users and exclude_users.user_ids, user_id) == true or
            contains(exclude_users and exclude_users.group_ids, group_id) == true then
        return false
    end

    if not effective_users.enable_all then
        if contains(effective_users and effective_users.user_ids, user_id) == false and
                contains(effective_users and effective_users.group_ids, group_id) == false then
            return false
        end
    end

    return true
end

local function check_watermark_app(conf)
    local app_id = ngx.var.http_app_id
    local watermark_conf = conf.watermark_conf
    local effective_apps = watermark_conf.effective_apps
    if effective_apps == nil then
        return false
    end
    if not effective_apps.enable_all then
        if contains(effective_apps and effective_apps.app_ids, app_id) == false then
            return false
        end
    end

    return true
end

local function check_watermark_enable(conf)
    local status = ngx.status
    if tonumber(status)<300  then
        local check_user = check_watermark_user(conf)
        core.log.warn("user check res: ",check_user)
        if check_user then
            core.log.warn("user check pass")
            local check_app = check_watermark_app(conf)
            if check_app then
                core.log.warn("app check pass")
                return true
            end
        end
    end
    return false
end

do
    function _M.body_filter(conf, ctx)
        local content_type = ngx.header['Content-Type']
        if not conf.enable then
            return
        end
        core.log.warn("content_type",content_type)
        if content_type ==nil or not content_type:find("text/html") then
            return
        end
        if check_watermark_enable(conf) then
            local depend_js = "<head><script src=\"https://asec-guanwang.oss-cn-guangzhou.aliyuncs.com/l-watermark.umd.js\"></script>"
            local body_start = '<head>'
            local body_end = '</body>'
            local script = "<script> %s </script>"
            script = script:format(get_watermark_conf(conf,ctx))

            local body = core.response.hold_body_chunk(ctx)
            if not body then
                return
            end
            local err

            body, _, err = re_sub(body, body_start, depend_js, "o")
            if err ~= nil then
                core.log.error("regex \"" .. body_start .. "\" substitutes failed:" .. err)
            end
            -- 获取pos位置后面的所有字符串
            local pattern = "(.-)</body>(.*)$"
            local _, afterBody = string.match(body, pattern)
            script = script..body_end..afterBody
            body, _, err = re_sub(body, body_end..afterBody, script, "o")
            if err ~= nil then
                core.log.error("regex \"" .. body_end .. "\" substitutes failed:" .. err)
            end

            -- 判断是否存在iframe
            ngx.arg[1] = body
            return
        end
    end

    function _M.header_filter(conf,ctx)
        if not conf.enable then
            return
        end
        local content_type = ngx.header['Content-Type']
        core.log.warn("content_type",content_type)
        if content_type ==nil or not content_type:find("text/html") then
            return
        end
        if check_watermark_enable(conf) then
            core.log.warn("content_length: ",ngx.header['Content-Length']," body length: ",body_length)

            core.response.set_header("Content-Length", body_length)
            core.log.warn("content_length after: ",ngx.header['Content-Length'])
        end
    end
end

return _M
