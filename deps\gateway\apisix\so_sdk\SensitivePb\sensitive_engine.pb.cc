// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sensitive_engine.proto

#include "sensitive_engine.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace SensitiveEngine {
PROTOBUF_CONSTEXPR SensitiveExtractParam::SensitiveExtractParam(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.dlp_rule_id_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.file_path_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.file_stream_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.src_proc_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.src_url_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.match_type_)*/0
  , /*decltype(_impl_.match_mode_)*/0
  , /*decltype(_impl_.update_cache_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SensitiveExtractParamDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SensitiveExtractParamDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SensitiveExtractParamDefaultTypeInternal() {}
  union {
    SensitiveExtractParam _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SensitiveExtractParamDefaultTypeInternal _SensitiveExtractParam_default_instance_;
PROTOBUF_CONSTEXPR SensitiveElementInfo::SensitiveElementInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.sensitive_element_data_)*/{}
  , /*decltype(_impl_.desc_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.rule_id_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SensitiveElementInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SensitiveElementInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SensitiveElementInfoDefaultTypeInternal() {}
  union {
    SensitiveElementInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SensitiveElementInfoDefaultTypeInternal _SensitiveElementInfo_default_instance_;
PROTOBUF_CONSTEXPR SensitiveDataInfo::SensitiveDataInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.sensitive_source_id_)*/{}
  , /*decltype(_impl_.src_paths_)*/{}
  , /*decltype(_impl_.file_name_result_)*/{}
  , /*decltype(_impl_.file_content_result_)*/{}
  , /*decltype(_impl_.file_text_result_)*/{}
  , /*decltype(_impl_.sensitive_data_id_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.sensitive_data_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.sensitive_data_category_id_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.sensitive_data_level_)*/0
  , /*decltype(_impl_.hit_)*/false
  , /*decltype(_impl_.file_type_suffix_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SensitiveDataInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SensitiveDataInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SensitiveDataInfoDefaultTypeInternal() {}
  union {
    SensitiveDataInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SensitiveDataInfoDefaultTypeInternal _SensitiveDataInfo_default_instance_;
PROTOBUF_CONSTEXPR SensitiveExtractPackage::SensitiveExtractPackage(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.trace_id_)*/{}
  , /*decltype(_impl_.sub_trace_id_)*/{}
  , /*decltype(_impl_.sensitive_data_infos_)*/{}
  , /*decltype(_impl_.extension_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.category_id_)*/0
  , /*decltype(_impl_.file_type_suffix_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SensitiveExtractPackageDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SensitiveExtractPackageDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SensitiveExtractPackageDefaultTypeInternal() {}
  union {
    SensitiveExtractPackage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SensitiveExtractPackageDefaultTypeInternal _SensitiveExtractPackage_default_instance_;
}  // namespace SensitiveEngine
static ::_pb::Metadata file_level_metadata_sensitive_5fengine_2eproto[4];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_sensitive_5fengine_2eproto[2];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_sensitive_5fengine_2eproto = nullptr;

const uint32_t TableStruct_sensitive_5fengine_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.dlp_rule_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.file_path_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.file_stream_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.src_proc_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.src_url_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.match_type_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.match_mode_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractParam, _impl_.update_cache_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveElementInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveElementInfo, _impl_.rule_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveElementInfo, _impl_.desc_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveElementInfo, _impl_.sensitive_element_data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.hit_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.sensitive_data_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.sensitive_data_name_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.sensitive_data_level_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.sensitive_data_category_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.sensitive_source_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.src_paths_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.file_name_result_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.file_content_result_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.file_text_result_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveDataInfo, _impl_.file_type_suffix_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractPackage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractPackage, _impl_.trace_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractPackage, _impl_.sub_trace_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractPackage, _impl_.category_id_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractPackage, _impl_.extension_name_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractPackage, _impl_.sensitive_data_infos_),
  PROTOBUF_FIELD_OFFSET(::SensitiveEngine::SensitiveExtractPackage, _impl_.file_type_suffix_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::SensitiveEngine::SensitiveExtractParam)},
  { 14, -1, -1, sizeof(::SensitiveEngine::SensitiveElementInfo)},
  { 23, -1, -1, sizeof(::SensitiveEngine::SensitiveDataInfo)},
  { 40, -1, -1, sizeof(::SensitiveEngine::SensitiveExtractPackage)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::SensitiveEngine::_SensitiveExtractParam_default_instance_._instance,
  &::SensitiveEngine::_SensitiveElementInfo_default_instance_._instance,
  &::SensitiveEngine::_SensitiveDataInfo_default_instance_._instance,
  &::SensitiveEngine::_SensitiveExtractPackage_default_instance_._instance,
};

const char descriptor_table_protodef_sensitive_5fengine_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026sensitive_engine.proto\022\017SensitiveEngin"
  "e\"\331\003\n\025SensitiveExtractParam\022\023\n\013dlp_rule_"
  "id\030\001 \001(\t\022\021\n\tfile_path\030\002 \001(\t\022\023\n\013file_stre"
  "am\030\003 \001(\014\022\020\n\010src_proc\030\004 \001(\t\022\017\n\007src_url\030\005 "
  "\001(\t\022D\n\nmatch_type\030\006 \001(\01620.SensitiveEngin"
  "e.SensitiveExtractParam.MatchType\022D\n\nmat"
  "ch_mode\030\007 \001(\01620.SensitiveEngine.Sensitiv"
  "eExtractParam.MatchMode\022\024\n\014update_cache\030"
  "\010 \001(\010\"\177\n\tMatchType\022\020\n\014kInvalidType\020\000\022\n\n\006"
  "kTrace\020\001\022\r\n\tkFileType\020\002\022\r\n\tkFileAttr\020\004\022\r"
  "\n\tkFileName\020\010\022\020\n\014kFileContent\020\020\022\025\n\021kSour"
  "ceProcAndUrl\020 \"=\n\tMatchMode\022\020\n\014kInvalidM"
  "ode\020\000\022\r\n\tkFilePath\020\001\022\017\n\013kFileStream\020\002\"U\n"
  "\024SensitiveElementInfo\022\017\n\007rule_id\030\001 \001(\005\022\014"
  "\n\004desc\030\002 \001(\t\022\036\n\026sensitive_element_data\030\003"
  " \003(\t\"\252\003\n\021SensitiveDataInfo\022\013\n\003hit\030\001 \001(\010\022"
  "\031\n\021sensitive_data_id\030\002 \001(\t\022\033\n\023sensitive_"
  "data_name\030\003 \001(\t\022\034\n\024sensitive_data_level\030"
  "\004 \001(\005\022\"\n\032sensitive_data_category_id\030\005 \001("
  "\t\022\033\n\023sensitive_source_id\030\006 \003(\t\022\021\n\tsrc_pa"
  "ths\030\007 \003(\t\022\?\n\020file_name_result\030\010 \003(\0132%.Se"
  "nsitiveEngine.SensitiveElementInfo\022B\n\023fi"
  "le_content_result\030\t \003(\0132%.SensitiveEngin"
  "e.SensitiveElementInfo\022\?\n\020file_text_resu"
  "lt\030\n \003(\0132%.SensitiveEngine.SensitiveElem"
  "entInfo\022\030\n\020file_type_suffix\030\013 \001(\010\"\312\001\n\027Se"
  "nsitiveExtractPackage\022\020\n\010trace_id\030\001 \003(\t\022"
  "\024\n\014sub_trace_id\030\002 \003(\t\022\023\n\013category_id\030\003 \001"
  "(\005\022\026\n\016extension_name\030\004 \001(\t\022@\n\024sensitive_"
  "data_infos\030\005 \003(\0132\".SensitiveEngine.Sensi"
  "tiveDataInfo\022\030\n\020file_type_suffix\030\006 \001(\010b\006"
  "proto3"
  ;
static ::_pbi::once_flag descriptor_table_sensitive_5fengine_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_sensitive_5fengine_2eproto = {
    false, false, 1246, descriptor_table_protodef_sensitive_5fengine_2eproto,
    "sensitive_engine.proto",
    &descriptor_table_sensitive_5fengine_2eproto_once, nullptr, 0, 4,
    schemas, file_default_instances, TableStruct_sensitive_5fengine_2eproto::offsets,
    file_level_metadata_sensitive_5fengine_2eproto, file_level_enum_descriptors_sensitive_5fengine_2eproto,
    file_level_service_descriptors_sensitive_5fengine_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_sensitive_5fengine_2eproto_getter() {
  return &descriptor_table_sensitive_5fengine_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_sensitive_5fengine_2eproto(&descriptor_table_sensitive_5fengine_2eproto);
namespace SensitiveEngine {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SensitiveExtractParam_MatchType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_sensitive_5fengine_2eproto);
  return file_level_enum_descriptors_sensitive_5fengine_2eproto[0];
}
bool SensitiveExtractParam_MatchType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 4:
    case 8:
    case 16:
    case 32:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::kInvalidType;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::kTrace;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::kFileType;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::kFileAttr;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::kFileName;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::kFileContent;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::kSourceProcAndUrl;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::MatchType_MIN;
constexpr SensitiveExtractParam_MatchType SensitiveExtractParam::MatchType_MAX;
constexpr int SensitiveExtractParam::MatchType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SensitiveExtractParam_MatchMode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_sensitive_5fengine_2eproto);
  return file_level_enum_descriptors_sensitive_5fengine_2eproto[1];
}
bool SensitiveExtractParam_MatchMode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr SensitiveExtractParam_MatchMode SensitiveExtractParam::kInvalidMode;
constexpr SensitiveExtractParam_MatchMode SensitiveExtractParam::kFilePath;
constexpr SensitiveExtractParam_MatchMode SensitiveExtractParam::kFileStream;
constexpr SensitiveExtractParam_MatchMode SensitiveExtractParam::MatchMode_MIN;
constexpr SensitiveExtractParam_MatchMode SensitiveExtractParam::MatchMode_MAX;
constexpr int SensitiveExtractParam::MatchMode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

// ===================================================================

class SensitiveExtractParam::_Internal {
 public:
};

SensitiveExtractParam::SensitiveExtractParam(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:SensitiveEngine.SensitiveExtractParam)
}
SensitiveExtractParam::SensitiveExtractParam(const SensitiveExtractParam& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SensitiveExtractParam* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.dlp_rule_id_){}
    , decltype(_impl_.file_path_){}
    , decltype(_impl_.file_stream_){}
    , decltype(_impl_.src_proc_){}
    , decltype(_impl_.src_url_){}
    , decltype(_impl_.match_type_){}
    , decltype(_impl_.match_mode_){}
    , decltype(_impl_.update_cache_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.dlp_rule_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.dlp_rule_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_dlp_rule_id().empty()) {
    _this->_impl_.dlp_rule_id_.Set(from._internal_dlp_rule_id(), 
      _this->GetArenaForAllocation());
  }
  _impl_.file_path_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.file_path_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_file_path().empty()) {
    _this->_impl_.file_path_.Set(from._internal_file_path(), 
      _this->GetArenaForAllocation());
  }
  _impl_.file_stream_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.file_stream_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_file_stream().empty()) {
    _this->_impl_.file_stream_.Set(from._internal_file_stream(), 
      _this->GetArenaForAllocation());
  }
  _impl_.src_proc_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.src_proc_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_src_proc().empty()) {
    _this->_impl_.src_proc_.Set(from._internal_src_proc(), 
      _this->GetArenaForAllocation());
  }
  _impl_.src_url_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.src_url_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_src_url().empty()) {
    _this->_impl_.src_url_.Set(from._internal_src_url(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.match_type_, &from._impl_.match_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.update_cache_) -
    reinterpret_cast<char*>(&_impl_.match_type_)) + sizeof(_impl_.update_cache_));
  // @@protoc_insertion_point(copy_constructor:SensitiveEngine.SensitiveExtractParam)
}

inline void SensitiveExtractParam::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.dlp_rule_id_){}
    , decltype(_impl_.file_path_){}
    , decltype(_impl_.file_stream_){}
    , decltype(_impl_.src_proc_){}
    , decltype(_impl_.src_url_){}
    , decltype(_impl_.match_type_){0}
    , decltype(_impl_.match_mode_){0}
    , decltype(_impl_.update_cache_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.dlp_rule_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.dlp_rule_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.file_path_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.file_path_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.file_stream_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.file_stream_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.src_proc_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.src_proc_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.src_url_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.src_url_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SensitiveExtractParam::~SensitiveExtractParam() {
  // @@protoc_insertion_point(destructor:SensitiveEngine.SensitiveExtractParam)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SensitiveExtractParam::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.dlp_rule_id_.Destroy();
  _impl_.file_path_.Destroy();
  _impl_.file_stream_.Destroy();
  _impl_.src_proc_.Destroy();
  _impl_.src_url_.Destroy();
}

void SensitiveExtractParam::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SensitiveExtractParam::Clear() {
// @@protoc_insertion_point(message_clear_start:SensitiveEngine.SensitiveExtractParam)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.dlp_rule_id_.ClearToEmpty();
  _impl_.file_path_.ClearToEmpty();
  _impl_.file_stream_.ClearToEmpty();
  _impl_.src_proc_.ClearToEmpty();
  _impl_.src_url_.ClearToEmpty();
  ::memset(&_impl_.match_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.update_cache_) -
      reinterpret_cast<char*>(&_impl_.match_type_)) + sizeof(_impl_.update_cache_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SensitiveExtractParam::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string dlp_rule_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_dlp_rule_id();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveExtractParam.dlp_rule_id"));
        } else
          goto handle_unusual;
        continue;
      // string file_path = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_file_path();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveExtractParam.file_path"));
        } else
          goto handle_unusual;
        continue;
      // bytes file_stream = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_file_stream();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string src_proc = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_src_proc();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveExtractParam.src_proc"));
        } else
          goto handle_unusual;
        continue;
      // string src_url = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_src_url();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveExtractParam.src_url"));
        } else
          goto handle_unusual;
        continue;
      // .SensitiveEngine.SensitiveExtractParam.MatchType match_type = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_match_type(static_cast<::SensitiveEngine::SensitiveExtractParam_MatchType>(val));
        } else
          goto handle_unusual;
        continue;
      // .SensitiveEngine.SensitiveExtractParam.MatchMode match_mode = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_match_mode(static_cast<::SensitiveEngine::SensitiveExtractParam_MatchMode>(val));
        } else
          goto handle_unusual;
        continue;
      // bool update_cache = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _impl_.update_cache_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SensitiveExtractParam::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:SensitiveEngine.SensitiveExtractParam)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string dlp_rule_id = 1;
  if (!this->_internal_dlp_rule_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_dlp_rule_id().data(), static_cast<int>(this->_internal_dlp_rule_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveExtractParam.dlp_rule_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_dlp_rule_id(), target);
  }

  // string file_path = 2;
  if (!this->_internal_file_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_file_path().data(), static_cast<int>(this->_internal_file_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveExtractParam.file_path");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_file_path(), target);
  }

  // bytes file_stream = 3;
  if (!this->_internal_file_stream().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_file_stream(), target);
  }

  // string src_proc = 4;
  if (!this->_internal_src_proc().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_src_proc().data(), static_cast<int>(this->_internal_src_proc().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveExtractParam.src_proc");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_src_proc(), target);
  }

  // string src_url = 5;
  if (!this->_internal_src_url().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_src_url().data(), static_cast<int>(this->_internal_src_url().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveExtractParam.src_url");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_src_url(), target);
  }

  // .SensitiveEngine.SensitiveExtractParam.MatchType match_type = 6;
  if (this->_internal_match_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      6, this->_internal_match_type(), target);
  }

  // .SensitiveEngine.SensitiveExtractParam.MatchMode match_mode = 7;
  if (this->_internal_match_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      7, this->_internal_match_mode(), target);
  }

  // bool update_cache = 8;
  if (this->_internal_update_cache() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(8, this->_internal_update_cache(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:SensitiveEngine.SensitiveExtractParam)
  return target;
}

size_t SensitiveExtractParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:SensitiveEngine.SensitiveExtractParam)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string dlp_rule_id = 1;
  if (!this->_internal_dlp_rule_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dlp_rule_id());
  }

  // string file_path = 2;
  if (!this->_internal_file_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_file_path());
  }

  // bytes file_stream = 3;
  if (!this->_internal_file_stream().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_file_stream());
  }

  // string src_proc = 4;
  if (!this->_internal_src_proc().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_src_proc());
  }

  // string src_url = 5;
  if (!this->_internal_src_url().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_src_url());
  }

  // .SensitiveEngine.SensitiveExtractParam.MatchType match_type = 6;
  if (this->_internal_match_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_match_type());
  }

  // .SensitiveEngine.SensitiveExtractParam.MatchMode match_mode = 7;
  if (this->_internal_match_mode() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_match_mode());
  }

  // bool update_cache = 8;
  if (this->_internal_update_cache() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SensitiveExtractParam::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SensitiveExtractParam::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SensitiveExtractParam::GetClassData() const { return &_class_data_; }


void SensitiveExtractParam::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SensitiveExtractParam*>(&to_msg);
  auto& from = static_cast<const SensitiveExtractParam&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:SensitiveEngine.SensitiveExtractParam)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_dlp_rule_id().empty()) {
    _this->_internal_set_dlp_rule_id(from._internal_dlp_rule_id());
  }
  if (!from._internal_file_path().empty()) {
    _this->_internal_set_file_path(from._internal_file_path());
  }
  if (!from._internal_file_stream().empty()) {
    _this->_internal_set_file_stream(from._internal_file_stream());
  }
  if (!from._internal_src_proc().empty()) {
    _this->_internal_set_src_proc(from._internal_src_proc());
  }
  if (!from._internal_src_url().empty()) {
    _this->_internal_set_src_url(from._internal_src_url());
  }
  if (from._internal_match_type() != 0) {
    _this->_internal_set_match_type(from._internal_match_type());
  }
  if (from._internal_match_mode() != 0) {
    _this->_internal_set_match_mode(from._internal_match_mode());
  }
  if (from._internal_update_cache() != 0) {
    _this->_internal_set_update_cache(from._internal_update_cache());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SensitiveExtractParam::CopyFrom(const SensitiveExtractParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:SensitiveEngine.SensitiveExtractParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SensitiveExtractParam::IsInitialized() const {
  return true;
}

void SensitiveExtractParam::InternalSwap(SensitiveExtractParam* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.dlp_rule_id_, lhs_arena,
      &other->_impl_.dlp_rule_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.file_path_, lhs_arena,
      &other->_impl_.file_path_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.file_stream_, lhs_arena,
      &other->_impl_.file_stream_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.src_proc_, lhs_arena,
      &other->_impl_.src_proc_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.src_url_, lhs_arena,
      &other->_impl_.src_url_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SensitiveExtractParam, _impl_.update_cache_)
      + sizeof(SensitiveExtractParam::_impl_.update_cache_)
      - PROTOBUF_FIELD_OFFSET(SensitiveExtractParam, _impl_.match_type_)>(
          reinterpret_cast<char*>(&_impl_.match_type_),
          reinterpret_cast<char*>(&other->_impl_.match_type_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SensitiveExtractParam::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_sensitive_5fengine_2eproto_getter, &descriptor_table_sensitive_5fengine_2eproto_once,
      file_level_metadata_sensitive_5fengine_2eproto[0]);
}

// ===================================================================

class SensitiveElementInfo::_Internal {
 public:
};

SensitiveElementInfo::SensitiveElementInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:SensitiveEngine.SensitiveElementInfo)
}
SensitiveElementInfo::SensitiveElementInfo(const SensitiveElementInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SensitiveElementInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.sensitive_element_data_){from._impl_.sensitive_element_data_}
    , decltype(_impl_.desc_){}
    , decltype(_impl_.rule_id_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.desc_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.desc_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_desc().empty()) {
    _this->_impl_.desc_.Set(from._internal_desc(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.rule_id_ = from._impl_.rule_id_;
  // @@protoc_insertion_point(copy_constructor:SensitiveEngine.SensitiveElementInfo)
}

inline void SensitiveElementInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.sensitive_element_data_){arena}
    , decltype(_impl_.desc_){}
    , decltype(_impl_.rule_id_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.desc_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.desc_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SensitiveElementInfo::~SensitiveElementInfo() {
  // @@protoc_insertion_point(destructor:SensitiveEngine.SensitiveElementInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SensitiveElementInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.sensitive_element_data_.~RepeatedPtrField();
  _impl_.desc_.Destroy();
}

void SensitiveElementInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SensitiveElementInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:SensitiveEngine.SensitiveElementInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.sensitive_element_data_.Clear();
  _impl_.desc_.ClearToEmpty();
  _impl_.rule_id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SensitiveElementInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 rule_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.rule_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string desc = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_desc();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveElementInfo.desc"));
        } else
          goto handle_unusual;
        continue;
      // repeated string sensitive_element_data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_sensitive_element_data();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveElementInfo.sensitive_element_data"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SensitiveElementInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:SensitiveEngine.SensitiveElementInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 rule_id = 1;
  if (this->_internal_rule_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_rule_id(), target);
  }

  // string desc = 2;
  if (!this->_internal_desc().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_desc().data(), static_cast<int>(this->_internal_desc().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveElementInfo.desc");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_desc(), target);
  }

  // repeated string sensitive_element_data = 3;
  for (int i = 0, n = this->_internal_sensitive_element_data_size(); i < n; i++) {
    const auto& s = this->_internal_sensitive_element_data(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveElementInfo.sensitive_element_data");
    target = stream->WriteString(3, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:SensitiveEngine.SensitiveElementInfo)
  return target;
}

size_t SensitiveElementInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:SensitiveEngine.SensitiveElementInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string sensitive_element_data = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.sensitive_element_data_.size());
  for (int i = 0, n = _impl_.sensitive_element_data_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      _impl_.sensitive_element_data_.Get(i));
  }

  // string desc = 2;
  if (!this->_internal_desc().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_desc());
  }

  // int32 rule_id = 1;
  if (this->_internal_rule_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_rule_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SensitiveElementInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SensitiveElementInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SensitiveElementInfo::GetClassData() const { return &_class_data_; }


void SensitiveElementInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SensitiveElementInfo*>(&to_msg);
  auto& from = static_cast<const SensitiveElementInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:SensitiveEngine.SensitiveElementInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.sensitive_element_data_.MergeFrom(from._impl_.sensitive_element_data_);
  if (!from._internal_desc().empty()) {
    _this->_internal_set_desc(from._internal_desc());
  }
  if (from._internal_rule_id() != 0) {
    _this->_internal_set_rule_id(from._internal_rule_id());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SensitiveElementInfo::CopyFrom(const SensitiveElementInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:SensitiveEngine.SensitiveElementInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SensitiveElementInfo::IsInitialized() const {
  return true;
}

void SensitiveElementInfo::InternalSwap(SensitiveElementInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.sensitive_element_data_.InternalSwap(&other->_impl_.sensitive_element_data_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.desc_, lhs_arena,
      &other->_impl_.desc_, rhs_arena
  );
  swap(_impl_.rule_id_, other->_impl_.rule_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SensitiveElementInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_sensitive_5fengine_2eproto_getter, &descriptor_table_sensitive_5fengine_2eproto_once,
      file_level_metadata_sensitive_5fengine_2eproto[1]);
}

// ===================================================================

class SensitiveDataInfo::_Internal {
 public:
};

SensitiveDataInfo::SensitiveDataInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:SensitiveEngine.SensitiveDataInfo)
}
SensitiveDataInfo::SensitiveDataInfo(const SensitiveDataInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SensitiveDataInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.sensitive_source_id_){from._impl_.sensitive_source_id_}
    , decltype(_impl_.src_paths_){from._impl_.src_paths_}
    , decltype(_impl_.file_name_result_){from._impl_.file_name_result_}
    , decltype(_impl_.file_content_result_){from._impl_.file_content_result_}
    , decltype(_impl_.file_text_result_){from._impl_.file_text_result_}
    , decltype(_impl_.sensitive_data_id_){}
    , decltype(_impl_.sensitive_data_name_){}
    , decltype(_impl_.sensitive_data_category_id_){}
    , decltype(_impl_.sensitive_data_level_){}
    , decltype(_impl_.hit_){}
    , decltype(_impl_.file_type_suffix_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.sensitive_data_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sensitive_data_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sensitive_data_id().empty()) {
    _this->_impl_.sensitive_data_id_.Set(from._internal_sensitive_data_id(), 
      _this->GetArenaForAllocation());
  }
  _impl_.sensitive_data_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sensitive_data_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sensitive_data_name().empty()) {
    _this->_impl_.sensitive_data_name_.Set(from._internal_sensitive_data_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.sensitive_data_category_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sensitive_data_category_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sensitive_data_category_id().empty()) {
    _this->_impl_.sensitive_data_category_id_.Set(from._internal_sensitive_data_category_id(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.sensitive_data_level_, &from._impl_.sensitive_data_level_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.file_type_suffix_) -
    reinterpret_cast<char*>(&_impl_.sensitive_data_level_)) + sizeof(_impl_.file_type_suffix_));
  // @@protoc_insertion_point(copy_constructor:SensitiveEngine.SensitiveDataInfo)
}

inline void SensitiveDataInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.sensitive_source_id_){arena}
    , decltype(_impl_.src_paths_){arena}
    , decltype(_impl_.file_name_result_){arena}
    , decltype(_impl_.file_content_result_){arena}
    , decltype(_impl_.file_text_result_){arena}
    , decltype(_impl_.sensitive_data_id_){}
    , decltype(_impl_.sensitive_data_name_){}
    , decltype(_impl_.sensitive_data_category_id_){}
    , decltype(_impl_.sensitive_data_level_){0}
    , decltype(_impl_.hit_){false}
    , decltype(_impl_.file_type_suffix_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.sensitive_data_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sensitive_data_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.sensitive_data_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sensitive_data_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.sensitive_data_category_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sensitive_data_category_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SensitiveDataInfo::~SensitiveDataInfo() {
  // @@protoc_insertion_point(destructor:SensitiveEngine.SensitiveDataInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SensitiveDataInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.sensitive_source_id_.~RepeatedPtrField();
  _impl_.src_paths_.~RepeatedPtrField();
  _impl_.file_name_result_.~RepeatedPtrField();
  _impl_.file_content_result_.~RepeatedPtrField();
  _impl_.file_text_result_.~RepeatedPtrField();
  _impl_.sensitive_data_id_.Destroy();
  _impl_.sensitive_data_name_.Destroy();
  _impl_.sensitive_data_category_id_.Destroy();
}

void SensitiveDataInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SensitiveDataInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:SensitiveEngine.SensitiveDataInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.sensitive_source_id_.Clear();
  _impl_.src_paths_.Clear();
  _impl_.file_name_result_.Clear();
  _impl_.file_content_result_.Clear();
  _impl_.file_text_result_.Clear();
  _impl_.sensitive_data_id_.ClearToEmpty();
  _impl_.sensitive_data_name_.ClearToEmpty();
  _impl_.sensitive_data_category_id_.ClearToEmpty();
  ::memset(&_impl_.sensitive_data_level_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.file_type_suffix_) -
      reinterpret_cast<char*>(&_impl_.sensitive_data_level_)) + sizeof(_impl_.file_type_suffix_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SensitiveDataInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool hit = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.hit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string sensitive_data_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_sensitive_data_id();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveDataInfo.sensitive_data_id"));
        } else
          goto handle_unusual;
        continue;
      // string sensitive_data_name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_sensitive_data_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveDataInfo.sensitive_data_name"));
        } else
          goto handle_unusual;
        continue;
      // int32 sensitive_data_level = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.sensitive_data_level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string sensitive_data_category_id = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_sensitive_data_category_id();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveDataInfo.sensitive_data_category_id"));
        } else
          goto handle_unusual;
        continue;
      // repeated string sensitive_source_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_sensitive_source_id();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveDataInfo.sensitive_source_id"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string src_paths = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_src_paths();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveDataInfo.src_paths"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .SensitiveEngine.SensitiveElementInfo file_name_result = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_file_name_result(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .SensitiveEngine.SensitiveElementInfo file_content_result = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_file_content_result(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .SensitiveEngine.SensitiveElementInfo file_text_result = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_file_text_result(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<82>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool file_type_suffix = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          _impl_.file_type_suffix_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SensitiveDataInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:SensitiveEngine.SensitiveDataInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool hit = 1;
  if (this->_internal_hit() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_hit(), target);
  }

  // string sensitive_data_id = 2;
  if (!this->_internal_sensitive_data_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sensitive_data_id().data(), static_cast<int>(this->_internal_sensitive_data_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveDataInfo.sensitive_data_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_sensitive_data_id(), target);
  }

  // string sensitive_data_name = 3;
  if (!this->_internal_sensitive_data_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sensitive_data_name().data(), static_cast<int>(this->_internal_sensitive_data_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveDataInfo.sensitive_data_name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_sensitive_data_name(), target);
  }

  // int32 sensitive_data_level = 4;
  if (this->_internal_sensitive_data_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_sensitive_data_level(), target);
  }

  // string sensitive_data_category_id = 5;
  if (!this->_internal_sensitive_data_category_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sensitive_data_category_id().data(), static_cast<int>(this->_internal_sensitive_data_category_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveDataInfo.sensitive_data_category_id");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_sensitive_data_category_id(), target);
  }

  // repeated string sensitive_source_id = 6;
  for (int i = 0, n = this->_internal_sensitive_source_id_size(); i < n; i++) {
    const auto& s = this->_internal_sensitive_source_id(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveDataInfo.sensitive_source_id");
    target = stream->WriteString(6, s, target);
  }

  // repeated string src_paths = 7;
  for (int i = 0, n = this->_internal_src_paths_size(); i < n; i++) {
    const auto& s = this->_internal_src_paths(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveDataInfo.src_paths");
    target = stream->WriteString(7, s, target);
  }

  // repeated .SensitiveEngine.SensitiveElementInfo file_name_result = 8;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_file_name_result_size()); i < n; i++) {
    const auto& repfield = this->_internal_file_name_result(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(8, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .SensitiveEngine.SensitiveElementInfo file_content_result = 9;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_file_content_result_size()); i < n; i++) {
    const auto& repfield = this->_internal_file_content_result(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(9, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .SensitiveEngine.SensitiveElementInfo file_text_result = 10;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_file_text_result_size()); i < n; i++) {
    const auto& repfield = this->_internal_file_text_result(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(10, repfield, repfield.GetCachedSize(), target, stream);
  }

  // bool file_type_suffix = 11;
  if (this->_internal_file_type_suffix() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(11, this->_internal_file_type_suffix(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:SensitiveEngine.SensitiveDataInfo)
  return target;
}

size_t SensitiveDataInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:SensitiveEngine.SensitiveDataInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string sensitive_source_id = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.sensitive_source_id_.size());
  for (int i = 0, n = _impl_.sensitive_source_id_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      _impl_.sensitive_source_id_.Get(i));
  }

  // repeated string src_paths = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.src_paths_.size());
  for (int i = 0, n = _impl_.src_paths_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      _impl_.src_paths_.Get(i));
  }

  // repeated .SensitiveEngine.SensitiveElementInfo file_name_result = 8;
  total_size += 1UL * this->_internal_file_name_result_size();
  for (const auto& msg : this->_impl_.file_name_result_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .SensitiveEngine.SensitiveElementInfo file_content_result = 9;
  total_size += 1UL * this->_internal_file_content_result_size();
  for (const auto& msg : this->_impl_.file_content_result_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .SensitiveEngine.SensitiveElementInfo file_text_result = 10;
  total_size += 1UL * this->_internal_file_text_result_size();
  for (const auto& msg : this->_impl_.file_text_result_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string sensitive_data_id = 2;
  if (!this->_internal_sensitive_data_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sensitive_data_id());
  }

  // string sensitive_data_name = 3;
  if (!this->_internal_sensitive_data_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sensitive_data_name());
  }

  // string sensitive_data_category_id = 5;
  if (!this->_internal_sensitive_data_category_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sensitive_data_category_id());
  }

  // int32 sensitive_data_level = 4;
  if (this->_internal_sensitive_data_level() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_sensitive_data_level());
  }

  // bool hit = 1;
  if (this->_internal_hit() != 0) {
    total_size += 1 + 1;
  }

  // bool file_type_suffix = 11;
  if (this->_internal_file_type_suffix() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SensitiveDataInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SensitiveDataInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SensitiveDataInfo::GetClassData() const { return &_class_data_; }


void SensitiveDataInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SensitiveDataInfo*>(&to_msg);
  auto& from = static_cast<const SensitiveDataInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:SensitiveEngine.SensitiveDataInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.sensitive_source_id_.MergeFrom(from._impl_.sensitive_source_id_);
  _this->_impl_.src_paths_.MergeFrom(from._impl_.src_paths_);
  _this->_impl_.file_name_result_.MergeFrom(from._impl_.file_name_result_);
  _this->_impl_.file_content_result_.MergeFrom(from._impl_.file_content_result_);
  _this->_impl_.file_text_result_.MergeFrom(from._impl_.file_text_result_);
  if (!from._internal_sensitive_data_id().empty()) {
    _this->_internal_set_sensitive_data_id(from._internal_sensitive_data_id());
  }
  if (!from._internal_sensitive_data_name().empty()) {
    _this->_internal_set_sensitive_data_name(from._internal_sensitive_data_name());
  }
  if (!from._internal_sensitive_data_category_id().empty()) {
    _this->_internal_set_sensitive_data_category_id(from._internal_sensitive_data_category_id());
  }
  if (from._internal_sensitive_data_level() != 0) {
    _this->_internal_set_sensitive_data_level(from._internal_sensitive_data_level());
  }
  if (from._internal_hit() != 0) {
    _this->_internal_set_hit(from._internal_hit());
  }
  if (from._internal_file_type_suffix() != 0) {
    _this->_internal_set_file_type_suffix(from._internal_file_type_suffix());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SensitiveDataInfo::CopyFrom(const SensitiveDataInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:SensitiveEngine.SensitiveDataInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SensitiveDataInfo::IsInitialized() const {
  return true;
}

void SensitiveDataInfo::InternalSwap(SensitiveDataInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.sensitive_source_id_.InternalSwap(&other->_impl_.sensitive_source_id_);
  _impl_.src_paths_.InternalSwap(&other->_impl_.src_paths_);
  _impl_.file_name_result_.InternalSwap(&other->_impl_.file_name_result_);
  _impl_.file_content_result_.InternalSwap(&other->_impl_.file_content_result_);
  _impl_.file_text_result_.InternalSwap(&other->_impl_.file_text_result_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.sensitive_data_id_, lhs_arena,
      &other->_impl_.sensitive_data_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.sensitive_data_name_, lhs_arena,
      &other->_impl_.sensitive_data_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.sensitive_data_category_id_, lhs_arena,
      &other->_impl_.sensitive_data_category_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SensitiveDataInfo, _impl_.file_type_suffix_)
      + sizeof(SensitiveDataInfo::_impl_.file_type_suffix_)
      - PROTOBUF_FIELD_OFFSET(SensitiveDataInfo, _impl_.sensitive_data_level_)>(
          reinterpret_cast<char*>(&_impl_.sensitive_data_level_),
          reinterpret_cast<char*>(&other->_impl_.sensitive_data_level_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SensitiveDataInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_sensitive_5fengine_2eproto_getter, &descriptor_table_sensitive_5fengine_2eproto_once,
      file_level_metadata_sensitive_5fengine_2eproto[2]);
}

// ===================================================================

class SensitiveExtractPackage::_Internal {
 public:
};

SensitiveExtractPackage::SensitiveExtractPackage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:SensitiveEngine.SensitiveExtractPackage)
}
SensitiveExtractPackage::SensitiveExtractPackage(const SensitiveExtractPackage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SensitiveExtractPackage* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.trace_id_){from._impl_.trace_id_}
    , decltype(_impl_.sub_trace_id_){from._impl_.sub_trace_id_}
    , decltype(_impl_.sensitive_data_infos_){from._impl_.sensitive_data_infos_}
    , decltype(_impl_.extension_name_){}
    , decltype(_impl_.category_id_){}
    , decltype(_impl_.file_type_suffix_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.extension_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.extension_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_extension_name().empty()) {
    _this->_impl_.extension_name_.Set(from._internal_extension_name(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.category_id_, &from._impl_.category_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.file_type_suffix_) -
    reinterpret_cast<char*>(&_impl_.category_id_)) + sizeof(_impl_.file_type_suffix_));
  // @@protoc_insertion_point(copy_constructor:SensitiveEngine.SensitiveExtractPackage)
}

inline void SensitiveExtractPackage::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.trace_id_){arena}
    , decltype(_impl_.sub_trace_id_){arena}
    , decltype(_impl_.sensitive_data_infos_){arena}
    , decltype(_impl_.extension_name_){}
    , decltype(_impl_.category_id_){0}
    , decltype(_impl_.file_type_suffix_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.extension_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.extension_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SensitiveExtractPackage::~SensitiveExtractPackage() {
  // @@protoc_insertion_point(destructor:SensitiveEngine.SensitiveExtractPackage)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SensitiveExtractPackage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.trace_id_.~RepeatedPtrField();
  _impl_.sub_trace_id_.~RepeatedPtrField();
  _impl_.sensitive_data_infos_.~RepeatedPtrField();
  _impl_.extension_name_.Destroy();
}

void SensitiveExtractPackage::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SensitiveExtractPackage::Clear() {
// @@protoc_insertion_point(message_clear_start:SensitiveEngine.SensitiveExtractPackage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.trace_id_.Clear();
  _impl_.sub_trace_id_.Clear();
  _impl_.sensitive_data_infos_.Clear();
  _impl_.extension_name_.ClearToEmpty();
  ::memset(&_impl_.category_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.file_type_suffix_) -
      reinterpret_cast<char*>(&_impl_.category_id_)) + sizeof(_impl_.file_type_suffix_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SensitiveExtractPackage::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string trace_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_trace_id();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveExtractPackage.trace_id"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string sub_trace_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_sub_trace_id();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveExtractPackage.sub_trace_id"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 category_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.category_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string extension_name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_extension_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "SensitiveEngine.SensitiveExtractPackage.extension_name"));
        } else
          goto handle_unusual;
        continue;
      // repeated .SensitiveEngine.SensitiveDataInfo sensitive_data_infos = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_sensitive_data_infos(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool file_type_suffix = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _impl_.file_type_suffix_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SensitiveExtractPackage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:SensitiveEngine.SensitiveExtractPackage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string trace_id = 1;
  for (int i = 0, n = this->_internal_trace_id_size(); i < n; i++) {
    const auto& s = this->_internal_trace_id(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveExtractPackage.trace_id");
    target = stream->WriteString(1, s, target);
  }

  // repeated string sub_trace_id = 2;
  for (int i = 0, n = this->_internal_sub_trace_id_size(); i < n; i++) {
    const auto& s = this->_internal_sub_trace_id(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveExtractPackage.sub_trace_id");
    target = stream->WriteString(2, s, target);
  }

  // int32 category_id = 3;
  if (this->_internal_category_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_category_id(), target);
  }

  // string extension_name = 4;
  if (!this->_internal_extension_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_extension_name().data(), static_cast<int>(this->_internal_extension_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "SensitiveEngine.SensitiveExtractPackage.extension_name");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_extension_name(), target);
  }

  // repeated .SensitiveEngine.SensitiveDataInfo sensitive_data_infos = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_sensitive_data_infos_size()); i < n; i++) {
    const auto& repfield = this->_internal_sensitive_data_infos(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // bool file_type_suffix = 6;
  if (this->_internal_file_type_suffix() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(6, this->_internal_file_type_suffix(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:SensitiveEngine.SensitiveExtractPackage)
  return target;
}

size_t SensitiveExtractPackage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:SensitiveEngine.SensitiveExtractPackage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string trace_id = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.trace_id_.size());
  for (int i = 0, n = _impl_.trace_id_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      _impl_.trace_id_.Get(i));
  }

  // repeated string sub_trace_id = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.sub_trace_id_.size());
  for (int i = 0, n = _impl_.sub_trace_id_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      _impl_.sub_trace_id_.Get(i));
  }

  // repeated .SensitiveEngine.SensitiveDataInfo sensitive_data_infos = 5;
  total_size += 1UL * this->_internal_sensitive_data_infos_size();
  for (const auto& msg : this->_impl_.sensitive_data_infos_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string extension_name = 4;
  if (!this->_internal_extension_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_extension_name());
  }

  // int32 category_id = 3;
  if (this->_internal_category_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_category_id());
  }

  // bool file_type_suffix = 6;
  if (this->_internal_file_type_suffix() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SensitiveExtractPackage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SensitiveExtractPackage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SensitiveExtractPackage::GetClassData() const { return &_class_data_; }


void SensitiveExtractPackage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SensitiveExtractPackage*>(&to_msg);
  auto& from = static_cast<const SensitiveExtractPackage&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:SensitiveEngine.SensitiveExtractPackage)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.trace_id_.MergeFrom(from._impl_.trace_id_);
  _this->_impl_.sub_trace_id_.MergeFrom(from._impl_.sub_trace_id_);
  _this->_impl_.sensitive_data_infos_.MergeFrom(from._impl_.sensitive_data_infos_);
  if (!from._internal_extension_name().empty()) {
    _this->_internal_set_extension_name(from._internal_extension_name());
  }
  if (from._internal_category_id() != 0) {
    _this->_internal_set_category_id(from._internal_category_id());
  }
  if (from._internal_file_type_suffix() != 0) {
    _this->_internal_set_file_type_suffix(from._internal_file_type_suffix());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SensitiveExtractPackage::CopyFrom(const SensitiveExtractPackage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:SensitiveEngine.SensitiveExtractPackage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SensitiveExtractPackage::IsInitialized() const {
  return true;
}

void SensitiveExtractPackage::InternalSwap(SensitiveExtractPackage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.trace_id_.InternalSwap(&other->_impl_.trace_id_);
  _impl_.sub_trace_id_.InternalSwap(&other->_impl_.sub_trace_id_);
  _impl_.sensitive_data_infos_.InternalSwap(&other->_impl_.sensitive_data_infos_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.extension_name_, lhs_arena,
      &other->_impl_.extension_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SensitiveExtractPackage, _impl_.file_type_suffix_)
      + sizeof(SensitiveExtractPackage::_impl_.file_type_suffix_)
      - PROTOBUF_FIELD_OFFSET(SensitiveExtractPackage, _impl_.category_id_)>(
          reinterpret_cast<char*>(&_impl_.category_id_),
          reinterpret_cast<char*>(&other->_impl_.category_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SensitiveExtractPackage::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_sensitive_5fengine_2eproto_getter, &descriptor_table_sensitive_5fengine_2eproto_once,
      file_level_metadata_sensitive_5fengine_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace SensitiveEngine
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::SensitiveEngine::SensitiveExtractParam*
Arena::CreateMaybeMessage< ::SensitiveEngine::SensitiveExtractParam >(Arena* arena) {
  return Arena::CreateMessageInternal< ::SensitiveEngine::SensitiveExtractParam >(arena);
}
template<> PROTOBUF_NOINLINE ::SensitiveEngine::SensitiveElementInfo*
Arena::CreateMaybeMessage< ::SensitiveEngine::SensitiveElementInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::SensitiveEngine::SensitiveElementInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::SensitiveEngine::SensitiveDataInfo*
Arena::CreateMaybeMessage< ::SensitiveEngine::SensitiveDataInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::SensitiveEngine::SensitiveDataInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::SensitiveEngine::SensitiveExtractPackage*
Arena::CreateMaybeMessage< ::SensitiveEngine::SensitiveExtractPackage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::SensitiveEngine::SensitiveExtractPackage >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
