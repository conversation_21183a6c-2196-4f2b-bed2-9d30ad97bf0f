local core = require("apisix.core")
local http = require("resty.http")
local json = require("cjson")
local re_sub = ngx.re.sub

local plugin_name = "asec-form-fill"

local schema = {
    type = "object",
    properties = {
        enable = { type = "boolean", default = false },
        app_id = { type = "string" }, -- 应用ID
        uri = { type = "string", default = "https://asec_platform_host/console/v1/form-fill" },
        form_config = { type = "object" }, -- 表单配置数据
    },
    required = {"enable"},
}

local _M = {
    version = 0.1,
    priority = 881,
    name = plugin_name,
    schema = schema,
}

function _M.check_schema(conf)
    return core.schema.check(schema, conf)
end

local function check_form_enable(conf)    
    if not conf.enable then
        return false
    end
    return true
end

-- 在access阶段获取表单凭证
local function get_form_credentials(conf, ctx)
    local form_config = conf.form_config or {}
    
    -- 从路由配置中获取app_id
    local app_id = conf.app_id
    if not app_id or app_id == "" then
        app_id = ctx.var.app_id or ""
        if app_id == "" then
            core.log.debug("未配置app_id，尝试从请求头获取")
            app_id = ngx.var.http_App_Id or ""
        end
    end

    -- 从认证插件获取用户信息
    local user_id = ngx.var.http_Asec_User_Id
    core.log.debug("用户信息: user_id=", user_id or "nil", ", app_id=", app_id)
    
    if not user_id then
        return
    end
    
    if not conf.uri or conf.uri == "" then
        return
    end
    
    if not app_id or app_id == "" then
        core.log.warn("app_id为空，可能影响表单代填凭证获取")
    end
    
    -- 获取Cookie中的JWT Token
    local cookie = ngx.var.http_cookie or ""
    local token = ""
    
    if cookie ~= "" then
        for cookie_pair in string.gmatch(cookie, "([^;]+)") do
            local name, value = string.match(cookie_pair, "^%s*([^=]+)=(.*)$")
            if name and value and string.gsub(name, "%s", "") == "asec_token" then
                token = string.gsub(value, "%s", "")
                break
            end
        end
    end
    
    if not token or token == "" then
        core.log.error("未获取到认证token，跳过凭证获取")
        return
    end
    
    -- 在access阶段调用后端接口获取凭证
    local httpc = http.new()
    if not httpc then
        core.log.error("表单代填获取后端请求创建失败")
        return
    end
    
    httpc:set_timeout(5000)
    
    local request_url = conf.uri .. "/account"
    if app_id and app_id ~= "" then
        request_url = request_url .. "?app_id=" .. app_id
    end
    
    core.log.debug("构建请求URL: ", request_url)
    
    local request_params = {
        method = "GET",
        headers = {
            ["Content-Type"] = "application/json",
            ["Authorization"] = "Bearer " .. token,
            ["User-Agent"] = "APISIX/FormFill-Plugin",
        },
        ssl_verify = false,
        timeout = 5000,
        keepalive = true,
        keepalive_timeout = 60000,
        keepalive_pool = 5,
    }
    
    local res, err = httpc:request_uri(request_url, request_params)
    
    if not res then
        core.log.error("HTTP请求失败: ", err or "unknown error")
        return
    end
    
    if res.status ~= 200 then
        core.log.warn("HTTP请求返回非200状态码: ", res.status)
        if res.body then
            core.log.debug("错误响应体: ", res.body)
        end
        return
    end
    
    local ok, response_data = pcall(json.decode, res.body)
    if ok and response_data and response_data.errcode == "0" then
        local credentials = response_data.credentials
        if credentials then
            local decoded = ngx.decode_base64(credentials)
            if decoded then
                local username, password = decoded:match("([^:]+):(.+)")
                if username and password then
                    -- 将凭证存储到上下文中，供body_filter阶段使用
                    ctx.form_fill_credentials = {
                        username = username,
                        password = password
                    }
                else
                    core.log.warn("凭证格式错误，无法解析用户名密码，decoded:", decoded)
                end
            else
                core.log.warn("base64解码失败，credentials:", credentials)
            end
        else
            core.log.warn("响应中无credentials字段，响应数据:", json.encode(response_data))
        end
    else
        core.log.warn("后端接口返回错误或JSON解析失败: ", res.body)
        if not ok then
            core.log.error("JSON解析错误: ", response_data)
        end
    end
end

-- 在body_filter阶段构建表单配置
local function build_form_config(conf, ctx)    
    local form_config = conf.form_config or {}
    
    -- 从路由配置中获取app_id
    local app_id = conf.app_id
    if not app_id or app_id == "" then
        app_id = ctx.var.app_id or ""
        if app_id == "" then
            app_id = ngx.var.http_App_Id or ""
        end
    end
   
    -- 从认证插件获取用户信息
    local user_id = ngx.var.http_Asec_User_Id
    core.log.debug("用户信息: user_id=", user_id or "nil", ", app_id=", app_id)
    
    if not user_id then
        core.log.error("未获取到用户ID，表单代填失败")
        return "{}"
    end
    
    local js_config = {
        APPType = "301",  -- 固定值，表示表单代填
        APPSubType = form_config.recognize_patterns or "smart",
        LoginUrl = form_config.login_url or "",  -- 传递登录URL给JavaScript
        AccountDataValue = "",
        PwdDataValue = "",
        -- 智能匹配数组
        autoAccountInputArr = {"username", "user", "account", "login", "email", "phone", "mobile"},
        autoPwdInputArr = {"password", "passwd", "pwd", "pass"},
        autoSubmitInputArr = {"submit", "login", "signin", "btn-login", "login-btn", "登录", "提交"}
    }
    
    -- 从上下文获取access阶段存储的凭证
    if ctx.form_fill_credentials then
        js_config.AccountDataValue = ctx.form_fill_credentials.username or ""
        js_config.PwdDataValue = ctx.form_fill_credentials.password or ""
    else
        core.log.debug("表单代填未获取到凭证，使用空值")
    end
    
    -- 根据表单识别模式设置不同配置
    if form_config.recognize_patterns == "precise" then
        -- 精确识别模式配置
        local config_data = form_config.data or {}
        js_config.AccountInputType = config_data.account_input_type or "fixed"
        js_config.AccountInputValue = config_data.username_input or ""
        js_config.PwdInputType = config_data.pwd_input_type or "fixed"
        js_config.PwdInputValue = config_data.password_input or ""
        js_config.SubmitInputType = config_data.submit_input_type or "fixed"
        js_config.SubmitInputValue = config_data.login_button or ""
        
        core.log.debug("精确识别模式配置: 账号输入类型=", js_config.AccountInputType, 
                      ", 账号输入值=", js_config.AccountInputValue,
                      ", 密码输入类型=", js_config.PwdInputType,
                      ", 密码输入值=", js_config.PwdInputValue)
    else
        -- 智能识别模式配置
        local config_data = form_config.data or {}
        js_config.AutoLogin = config_data.auto_login or "1"
        js_config.AutoLoginPed = config_data.auto_login_ped or "300"
        
        core.log.debug("智能识别模式配置: 自动登录=", js_config.AutoLogin, 
                      ", 登录间隔=", js_config.AutoLoginPed)
    end
    
    local ok, config_json = pcall(json.encode, js_config)
    if not ok then
        core.log.error("Failed to encode form config: ", config_json)
        return "{}"
    end
    return config_json
end

function _M.access(conf, ctx)
    if not conf.enable then
        return
    end

    get_form_credentials(conf, ctx)
end

function _M.body_filter(conf, ctx)
    local request_uri = ngx.var.request_uri or ""

    if not conf.enable then
        return
    end
    
    -- 检查Content-Type是否为HTML
    local content_type = ngx.header.content_type
    if not content_type or not content_type:find("text/html") then
        return
    end
    
    -- 检查是否为登录页面
    local form_config = conf.form_config or {}
    local login_url = form_config.login_url or ""
    
    if login_url == "" then
        core.log.debug("未配置登录URL，跳过表单代填处理")
        return
    end
    
    -- 解析登录URL路径
    local login_path = login_url:match("^https?://[^/]+(/.*)")
    if not login_path then
        login_path = login_url
    end
    
    -- core.log.debug("登录URL路径: ", login_path, ", 当前请求URI: ", request_uri)
    
    -- 检查当前请求是否匹配登录URL
    if not request_uri:find(login_path, 1, true) then
        -- core.log.debug("当前请求URI不匹配登录URL，跳过表单代填处理: ", request_uri)
        return
    end
    
    -- core.log.debug("表单代填插件激活，开始处理登录页面: ", request_uri)
    
    -- 构建静态JS文件URL
    local scheme = ngx.var.scheme
    local host = ngx.var.host
    
    -- 优先从请求头获取真实端口，处理端口映射场景
    local real_port = ngx.var.http_x_forwarded_port or ngx.var.http_x_real_port
    if not real_port or real_port == "" then
        -- 如果没有转发端口头，尝试从Host头解析
        local host_header = ngx.var.http_host or ""
        local host_port = host_header:match(":(%d+)$")
        if host_port then
            real_port = host_port
        else
            real_port = ngx.var.server_port
        end
    end
    
    local base_url = scheme .. "://" .. host
    if (scheme == "http" and real_port ~= "80") or (scheme == "https" and real_port ~= "443") then
        base_url = base_url .. ":" .. real_port
    end
    
    -- core.log.debug("构建JS URL - scheme:", scheme, ", host:", host, ", real_port:", real_port, ", base_url:", base_url)
    
    local static_js_url = base_url .. "/.asec-gateway/asec-internal/js/l-form.umd.js"
    
    local body = core.response.hold_body_chunk(ctx)
    if not body then
        body = ""
    end
    
    -- core.log.debug("处理完整响应体，长度: ", string.len(body))
    
    -- 直接在响应体末尾添加脚本，不做任何复杂构建
    local config_json = build_form_config(conf, ctx)
    body = body .. '<script src="' .. static_js_url .. '"></script>'
    body = body .. '<script>if(typeof FormFill !== "undefined"){FormFill.init(' .. config_json .. ');}else{console.error("FormFill not loaded");}</script>'
    
    ngx.arg[1] = body
end

return _M